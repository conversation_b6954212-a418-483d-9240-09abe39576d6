/* CSS Grid for Key Features */
.key-features {
    padding: 2rem;
    text-align: center;
}

.key-features h1 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
}

.features-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    align-items: start;
}

.features-grid div {
    background: #f9f9f9;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.features-grid h2 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.features-grid p {
    font-size: 1rem;
    color: #555;
}