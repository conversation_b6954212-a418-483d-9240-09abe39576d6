// This file is no longer needed. See src/middleware.js for the active middleware.

export function normalizeTheLocale(locale) {
  // Placeholder implementation
  return locale.toLowerCase();
}

export function notFound(payload) {
  return (context) => {
    console.error("Not found handler invoked.");
    return new Response(null, { status: 404 });
  };
}

export function redirectToDefaultLocale(payload) {
  return (context) => {
    console.log("Redirecting to default locale.");
    return new Response(null, { status: 302 });
  };
}

export function redirectToFallback(payload) {
  return (context) => {
    console.log("Redirecting to fallback.");
    return new Response(null, { status: 302 });
  };
}

export function requestHasLocale(locales) {
  return (context) => {
    const url = context.url;
    return locales.some((locale) => url.pathname.includes(`/${locale}`));
  };
}