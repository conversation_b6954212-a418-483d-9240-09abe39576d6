// This file is no longer needed. See src/middleware.js for the active middleware.

import createI18nMiddleware from "./middleware.js";

function loadMiddleware(app) {
  console.log("Loading middleware with app:", app);

  const i18nConfig = {
    defaultLocale: "en",
    locales: ["en", "fr", "am"],
  };
  const base = "/";
  const trailingSlash = false;
  const format = "html";

  app.use((req, res, next) => {
    console.log("Middleware invoked for request:", req.url);
    next();
  });

  app.use(createI18nMiddleware(i18nConfig, base, trailingSlash, format));

  console.log("Middleware loaded successfully.");
}

export default loadMiddleware;