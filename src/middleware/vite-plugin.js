import { defineConfig } from "vite";
import loadMiddleware from "../middleware/loadMiddleware.js";

// This file is no longer needed. See src/middleware.js for the active middleware.

export default defineConfig({
  plugins: [
    {
      name: "vite-plugin-middleware",
      configureServer(server) {
        console.log("Configuring server with middleware...");
        console.log("Server middlewares:", server.middlewares);
        loadMiddleware(server.middlewares);
        console.log("Middleware loaded into server.");
        console.log("Server configured successfully.");
      },
    },
  ],
});