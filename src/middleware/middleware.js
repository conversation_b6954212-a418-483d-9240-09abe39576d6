// This file is no longer needed. See src/middleware.js for the active middleware.
// import { REROUTE_DIRECTIVE_HEADER, ROUTE_TYPE_HEADER } from "../core/constants.js";
// import { isRequestServerIsland, requestIs404Or500 } from "../core/routing/match.js";
// import {
//   normalizeTheLocale,
//   notFound,
//   redirectToDefaultLocale,
//   redirectToFallback,
//   requestHasLocale
// } from "./index.js";

// function createI18nMiddleware(i18n, base, trailingSlash, format) {
//   if (!i18n) return (_, next) => next();
//   const payload = {
//     ...i18n,
//     trailingSlash,
//     base,
//     format,
//     domains: {}
//   };
//   const _redirectToDefaultLocale = redirectToDefaultLocale(payload);
//   const _noFoundForNonLocaleRoute = notFound(payload);
//   const _requestHasLocale = requestHasLocale(payload.locales);
//   const _redirectToFallback = redirectToFallback(payload);
//   const prefixAlways = (context, response) => {
//     const url = context.url;
//     if (url.pathname === base + "/" || url.pathname === base) {
//       return _redirectToDefaultLocale(context);
//     } else if (!_requestHasLocale(context)) {
//       return _noFoundForNonLocaleRoute(context, response);
//     }
//     return void 0;
//   };
//   const prefixOtherLocales = (context, response) => {
//     let pathnameContainsDefaultLocale = false;
//     const url = context.url;
//     for (const segment of url.pathname.split("/")) {
//       if (normalizeTheLocale(segment) === normalizeTheLocale(i18n.defaultLocale)) {
//         pathnameContainsDefaultLocale = true;
//         break;
//       }
//     }
//     if (pathnameContainsDefaultLocale) {
//       const newLocation = url.pathname.replace(`/${i18n.defaultLocale}`, "");
//       response.headers.set("Location", newLocation);
//       return _noFoundForNonLocaleRoute(context);
//     }
//     return void 0;
//   };
//   return async (context, next) => {
//     const response = await next();

//     // Always return a Response object
//     if (!(response instanceof Response)) {
//       return new Response(null, { status: 200 });
//     }

//     const type = response.headers.get(ROUTE_TYPE_HEADER);
//     console.log("Response type:", type);

//     if (type === "rewrite") {
//       return response;
//     }

//     const url = context.url;
//     const is404or500 = requestIs404Or500(context);
//     if (is404or500) {
//       return _noFoundForNonLocaleRoute(context, response);
//     }

//     if (url.pathname.startsWith(base)) {
//       return prefixAlways(context, response) || response;
//     }

//     const hasLocale = _requestHasLocale(context);
//     const isServerIsland = isRequestServerIsland(context);
//     if (!hasLocale && !isServerIsland) {
//       return _redirectToFallback(context, response);
//     }

//     return response;
//   };
// }

// export default createI18nMiddleware;