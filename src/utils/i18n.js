// Import all translation files
import en from '../i18n/en.json';
import fr from '../i18n/fr.json';

// Define translations and supported languages
const translations = { en, fr };
const defaultLang = 'en';
const languages = Object.keys(translations);

/**
 * Get the preferred language from browser
 * Safe to use in static environments (returns defaultLang if window is not available)
 */
export function getPreferredLanguage() {
  if (typeof window === 'undefined') return defaultLang;
  
  try {
    const browserLang = (navigator.language || defaultLang).split('-')[0];
    return languages.includes(browserLang) ? browserLang : defaultLang;
  } catch (e) {
    return defaultLang;
  }
}

/**
 * Extract language from URL path
 * @param {URL | string} url - URL object or path string
 * @returns {string} Detected language code
 */
export function getLangFromUrl(url) {
  try {
    const pathname = typeof url === 'string' ? url : url.pathname;
    const pathSegments = pathname.split('/').filter(Boolean);
    const lang = pathSegments[0]; // Get the first segment, which should be the language
    return languages.includes(lang) ? lang : defaultLang;
  } catch (e) {
    return defaultLang;
  }
}

/**
 * Get translation function for a specific locale
 * @param {string} locale - Target locale
 * @returns {Object} Translation utilities
 */
export function useTranslations(locale) {
  // Ensure the locale is valid, fallback to default
  const validLocale = languages.includes(locale) ? locale : defaultLang;
  
  return {
    t: (key, fallback = '') => {
      if (!key) return fallback || '';
      
      // Handle nested keys (e.g., 'nav.home')
      const keys = key.split('.');
      let result = translations[validLocale];
      
      try {
        // Traverse the nested object
        for (const k of keys) {
          result = result?.[k];
          if (result === undefined) break;
        }
      } catch (e) {
        console.warn(`Translation error for key "${key}":`, e);
        return fallback || key;
      }
      
      // Fallback to default language if translation not found
      if (result === undefined && validLocale !== defaultLang) {
        return useTranslations(defaultLang).t(key, fallback || key);
      }
      
      return result !== undefined ? result : (fallback || key);
    },
    
    // Helper to get all translations for a specific key across all languages
    getAllTranslations: (key) => {
      const result = {};
      languages.forEach(lang => {
        result[lang] = useTranslations(lang).t(key, '');
      });
      return result;
    }
  };
}

// Export all translations for static generation
export const allTranslations = translations;