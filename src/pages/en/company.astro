---
import BaseLayout from "../../layouts/BaseLayout.astro";
import Modal from "../../components/Modal.astro";
---

<BaseLayout title="Cash Management">
  <!-- Hero Component -->
  <section class="hero page no-bg">
    <div class="hero-main">
        <div class="hero-main-text">
            <h1>Cornerstone of Banking Software Innovation</h1>
            <p style="max-width: unset;">We are empowering African economies and inspiring generations.</p>
        </div>
    </div>
  </section>

  <!-- Main Content -->
  <article class="content">

    <!-- Core Values -->
    <section class="content-group no-padding-top max-width">
        <div class="info-row">
            <div class="row-details small">
                <div>
                    <h3 class="header">Core Values</h3>
                    <p>Grey Parrot IO is built on a trinity of values that form the essence of our mission. These three pillars not only define our approach to business but also embody our vision for transforming the banking landscape across Africa.</p>
                </div>
            </div>
            <div class="row-image large"><img src="./images/company/core-value.jpg" alt="Grey Parrot Core Value" loading="lazy"></div>
        </div>
    </section>

    <!-- Values -->
    <section class="content-group product-description-section">
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/medal.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Excellence</h5>
                <p>We are committed to delivering unparalleled value to our clients through superior solutions. Mediocrity has no place in our world – we strive for excellence in every line of code, every customer interaction, and every business decision. Our dedication to excellence ensures that our clients receive nothing but the best, setting new standards in the banking software industry.</p>
            </div>
        </div>
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/elearning-exchange.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Continuous Learning</h5>
                <p>Excellence is a journey, not a destination. We embrace a culture of perpetual growth and improvement, recognizing that the pursuit of knowledge is the foundation of innovation. By eagerly learning from industry best practices, our clients' experiences, and emerging technologies, we continuously refine our solutions to not just meet, but anticipate our customers' evolving needs. Our commitment to learning keeps us at the cutting edge of banking technology.</p>
            </div>
        </div>
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/healtcare.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Giving Back</h5>
                <p>We acknowledge that our success originates from our Creator, built on the shoulders of those who came before us and the support of our community. Gratitude is at the heart of our operations – we believe in the power of reciprocity and the responsibility to pay it forward. Through sharing our knowledge, volunteering our time, and contributing our financial resources, we aim to create a positive ripple effect in society. Our commitment to giving back ensures that as we grow, our community grows with us.</p>
            </div>
        </div>
    </section>

    <!-- Mission -->
    <section class="content-group max-width no-padding-top">
        <div class="info-row">
            <div class="row-details mission" style="padding-left: 0;">
                <h3 class="header">Our Mission</h3>
                <p>Grey Parrot's mission is to empower banks and financial institutions with world-class banking software tailored to the African market.</p>
                <p>We differentiate ourselves by embedding directly with our customers, ensuring fit-for-purpose solutions through our deep understanding of African banking needs and superior technical ability.</p>
                <p>Our goal is to drive innovation and efficiency in the financial sector, contributing to the broader economic development of Africa.</p>
            </div>
            <div class="row-image no-padding"><img src="./images/company/mission.jpg" class="width-100" alt="Grey Parrot Mission" loading="lazy"></div>
        </div>
    </section>

    <section class="content-group banner-section">
        <div class="banner-inner info-section">
            <h2>Book a Consultation</h2>
            <p>Ready to take your project to the next level? Our expert team is here to help you design and implement the perfect solution for your needs.</p>
        </div>
        <form class="banner-inner subscription-section" action="javascript:void(0);" id="request-demo-2-form">
            <div class="form-group">
                <div class="alert alert-success d-none" id="alert-success-2">Thank you! Your form has been successfully submitted. Our team will review your information and get back to you shortly</div>
                <div class="alert alert-danger d-none" id="alert-danger-2">We're sorry, but there was an issue submitting your form. Please try again later or contact our support team if the problem persists.</div>
                <label for="full-name">Name</label>
                <div class="form-row">
                    <input type="text" name="full-name" id="full-name" class="max-width-auto" required>
                    <input type="hidden" name="product" readonly id="product" value="consultation" class="max-width-auto">
                </div>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <div class="form-row">
                    <input type="email" name="email" id="email" class="max-width-auto" required>
                </div>
            </div>
            <div class="form-group">
                <button class="button" type="submit" id="request-demo-2-submit-button">Submit Request</button>
            </div>
        </form>
    </section>
  </article>

  <Modal product="cash-management" />

  <script slot="custom-script" src="/js/tab.js"></script>
  <script slot="custom-script" src="/js/product-demo-form.js"></script>
</BaseLayout>