---
import BaseLayout from "../../../layouts/BaseLayout.astro";
import Modal from "../../../components/Modal.astro";
---

<BaseLayout title="Omnicollect">
  <!-- Hero Component -->
  <section class="hero page">
    <div class="hero-main">
        <div class="hero-main-text text-center">
            <h1>Payments and Collections Management Platform</h1>
            <p>Designed for banks, corporate merchants, and their customers, our omnichannel platform caters to diverse payment preferences, from cash to digital options ensuring secure, error-free reconciliation.</p>
            <div class="flex wrap">
                <a href="javascript:void(0);" data-popup-trigger="#request-demo" class="button margin-right no-shrink">Request a Demo</a>
                <a href="/case-studies" class="link flex no-shrink"><span>See Case Study</span><img src="./images/arrow-red.png" loading="lazy" alt="Grey Parrot"></a>
            </div>
        </div>
    </div>
  </section>

  <!-- Main Content -->
  <article class="content product-page">

    <!-- Hero Banner -->
    <section class="content-group hero-banner">
        <img src="./images/products/lg/omnicollect.png" loading="lazy" class="product-page-image" alt="Grey Parrot IO Omnicollect">
    </section>

    <!-- Services -->
    <section class="content-group product-description-section">
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/payment.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Streamlined Onboarding and Flexible Payment Options</h5>
                <p>Our platform offers self-onboarding for individuals and corporations, enabling agents to facilitate placements in the field. Customers can choose suitable payment cycles, and our platform provides on-demand or underwriter-approved quotes.</p>
            </div>
        </div>
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/settings.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Enhanced Premium Management</h5>
                <p>Our platform offers flexible payment options, granular allocation, transparent tracking, convenient statements, and installment plans, streamlining premium management for both customers and administrators.</p>
            </div>
        </div>
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/checkmark-circle.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Automated Reconciliation</h5>
                <p>Our platform streamlines reconciliation processes for various accounts, including transit, teller, cash center, vault, and central bank accounts. By integrating with your core banking system, we eliminate manual errors and ensure accurate debit/credit transactions.</p>
            </div>
        </div>
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/notification.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Enhanced Notification and Reporting</h5>
                <p>Our platform delivers timely and personalized notifications, offering comprehensive reporting capabilities that track premium payments, customer debts, policy contributions, and split payments, ensuring compliance and facilitating informed decision-making.</p>
            </div>
        </div>
    </section>
    
    <section class="content-group banner-section">
        <div class="banner-inner info-section">
            <h1>Streamlined Integration for Enhanced Efficiency</h1>
            <p class="margin-bottom-large">Our platform seamlessly connects with your bank's internal systems, including notification channels, core banking, and active director platforms, optimizing operations and ensuring a unified user experience. Additionally, our integration with the GP Payments & Collections Platform (Omnicollect) enables efficient premium payments and collections.</p>
            <div class="text-center-sm">
                <a href="javascript:void(0);" data-popup-trigger="#request-demo" class="button inline-block">Request a Demo</a>
            </div>
        </div>
        <div class="banner-inner subscription-section">
            <img src="./images/products/lg/omnicollect-mockup.png" width="100%" alt="Grey Parrot IO Payments and Collections">
        </div>
    </section>

    <!-- <section class="content-group group-section">
        <h3 class="header">Customer Success Stories</h3>
    </section> -->

    <!-- <section class="content-group no-padding-top">
        <div class="tabs">
            <button class="tab-btn active" data-tab="tab1">Stanbic Bank Zambia</button>
            <button class="tab-btn" data-tab="tab2">Armaguard Security Ltd</button>
            <button class="tab-btn" data-tab="tab3">Brompton Securities</button>
        </div>
          
        <div class="tab-content active" id="tab1">
            <blockquote class="quote">
                <h4><q>Figma ipsum component variant main layer. Component arrange figma outline bullet line vector inspect</q></h4>
                <h4 class="text-bold">Figma ipsum component variant</h4>
                <a href="/case-studies" class="link flex no-shrink"><span>Read Case Study</span><img src="./images/arrow-red.png" loading="lazy" alt="Grey Parrot"></a>
            </blockquote>
        </div>
        <div class="tab-content" id="tab2">
            <blockquote class="quote">
                <h4><q>Figma ipsum component variant main layer. Component arrange figma outline bullet line vector inspect</q></h4>
                <h4 class="text-bold">Figma ipsum component variant</h4>
                <a href="/case-studies" class="link flex no-shrink"><span>Read Case Study</span><img src="./images/arrow-red.png" loading="lazy" alt="Grey Parrot"></a>
            </blockquote>
        </div>
        <div class="tab-content" id="tab3">
            <blockquote class="quote">
                <h4><q>Figma ipsum component variant main layer. Component arrange figma outline bullet line vector inspect</q></h4>
                <h4 class="text-bold">Figma ipsum component variant</h4>
                <a href="/case-studies" class="link flex no-shrink"><span>Read Case Study</span><img src="./images/arrow-red.png" loading="lazy" alt="Grey Parrot"></a>
            </blockquote>
        </div>
    </section> -->

    <section class="content-group group-section">
        <h3 class="header">Frequently Asked Questions</h3>
    </section>

    <section class="content-group no-padding-top margin-bottom-huge">
        <section class="faq-section">
            <details>
                <summary>
                    <b class="text-bold">What types of payments does your platform support? </b>
                    <span class="closed"><img src="./images/plus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                    <span class="opened"><img src="./images/minus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                </summary>
                <ul>
                    <span>Our platform is designed to accommodate a wide range of payment methods, including:</span>
                    <ul>
                        <li>Mobile money</li>
                        <li>Cheques</li>
                        <li>Credit and debit cards</li>
                        <li>Cash</li>
                        <li>Other electronic payment methods</li>
                    </ul>
                    <span>This comprehensive support ensures flexibility for your customers and helps streamline your payment processes across various transaction types.</span>
                </ul>
            </details>
        
            <details>
                <summary>
                    <b class="text-bold">Can your platform be integrated with our existing systems?</b>
                    <span class="closed"><img src="./images/plus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                    <span class="opened"><img src="./images/minus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                </summary>
                <ul>
                    <span>Yes, our platform offers extensive integration capabilities. It can be seamlessly integrated with:</span>
                    <ul>
                        <li>Enterprise Resource Planning (ERP) systems</li>
                        <li>Customer Relationship Management (CRM) software</li>
                        <li>Reconciliation systems</li>
                        <li>Other business software you may currently use</li>
                    </ul>
                    <span>This flexibility allows you to incorporate our platform into your existing technology ecosystem, enhancing overall efficiency and data consistency across your organization.</span>
                </ul>
            </details>
        
            <details>
                <summary>
                    <b class="text-bold">Is your platform cloud-based or on-premises?</b>
                    <span class="closed"><img src="./images/plus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                    <span class="opened"><img src="./images/minus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                </summary>
                <p>Our platform is primarily designed for on-premise deployment. However, we offer cloud-ready options to accommodate client preferences. You can choose the deployment model that best fits your organization's needs and infrastructure requirements.</p>
            </details>
        </section>
    </section>

    <section class="content-group banner-section">
        <div class="banner-inner info-section">
            <h2>Request Your Free Demo NOW</h2>
            <p>Experience seamless payments and collections tailored to your business. Request a demo now and discover how our platform can simplify transactions, boost efficiency, and drive growth!</p>
        </div>
        <form class="banner-inner subscription-section" action="javascript:void(0);" id="request-demo-2-form">
            <div class="form-group">
                <div class="alert alert-success d-none" id="alert-success-2">Thank you! Your form has been successfully submitted. Our team will review your information and get back to you shortly</div>
                <div class="alert alert-danger d-none" id="alert-danger-2">We're sorry, but there was an issue submitting your form. Please try again later or contact our support team if the problem persists.</div>
                <label for="full-name">Name</label>
                <div class="form-row">
                    <input type="text" name="full-name" id="full-name" class="max-width-auto" required>
                    <input type="hidden" name="product" readonly id="product" value="payment-collections" class="max-width-auto">
                </div>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <div class="form-row">
                    <input type="email" name="email" id="email" class="max-width-auto" required>
                </div>
            </div>
            <div class="form-group">
                <button class="button" type="submit" id="request-demo-2-submit-button">Submit Request</button>
            </div>
        </form>
    </section>
  </article>

  <Modal product="payment-collections" />

  <script slot="custom-script" src="/js/tab.js"></script>
  <script slot="custom-script" src="/js/popup.js"></script>
  <script slot="custom-script" src="/js/product-demo-form.js"></script>
</BaseLayout>