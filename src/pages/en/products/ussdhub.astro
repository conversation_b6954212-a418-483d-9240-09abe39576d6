---
import Modal from "../../../components/Modal.astro";
import BaseLayout from "../../../layouts/BaseLayout.astro";
---

<BaseLayout title="USSDhub">
  <!-- Hero Component -->
  <section class="hero page">
    <div class="hero-main">
        <div class="hero-main-text text-center">
            <h1>Rapid Deployment, Robust Security, and High-Impact Results</h1>
            <p>Our no-code USSD platform enables businesses to swiftly deploy innovative solutions within minutes. With unparalleled security and performance, USSDHub delivers a powerful and cost-effective platform for enhancing customer engagement and driving growth.</p>
            <div class="flex wrap">
                <a href="javascript:void(0);" data-popup-trigger="#request-demo" class="button margin-right no-shrink">Request a Demo</a>
                <a href="/case-studies" class="link flex no-shrink"><span>See Case Study</span><img src="./images/arrow-red.png" loading="lazy" alt="Grey Parrot"></a>
            </div>
        </div>
    </div>
  </section>

  <!-- Main Content -->
  <article class="content product-page">

    <!-- Hero Banner -->
    <section class="content-group hero-banner">
        <img src="./images/products/lg/ussdhub.png" loading="lazy" class="product-page-image" alt="Grey Parrot IO USSDhub">
    </section>

    <!-- Services -->
    <section class="content-group product-description-section">
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/rocket.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Effortless Deployment, Exceptional Results</h5>
                <p>Our no-code USSD platform eliminates the need for coding, allowing you to rapidly deploy cutting-edge solutions. Save significant time and resources while driving innovation and enhancing customer engagement.</p>
            </div>
        </div>
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/cloud-server.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Scalable Application Hosting</h5>
                <p>The platform supports an unlimited number of USSD applications, facilitating the development of diverse solutions tailored to specific business needs. Examples include Mobile Banking, Loan Origination, Savings Platforms, and Customer Support systems.</p>
            </div>
        </div>
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/chart-bar-line.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Real-Time Design Visualization</h5>
                <p>The platform provides instant visual feedback on design modifications, streamlining the development process and minimizing errors.</p>
            </div>
        </div>
        <!-- <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/coins.png" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Enhanced Notification and Reporting</h5>
                <p>Our platform delivers timely and personalized notifications, offering comprehensive reporting capabilities that track premium payments, customer debts, policy contributions, and split payments, ensuring compliance and facilitating informed decision-making.</p>
            </div>
        </div> -->
    </section>
    
    <section class="content-group banner-section">
        <div class="banner-inner info-section">
            <h1>Enhanced Reporting Capabilities</h1>
            <p class="margin-bottom-large">Centralized control panel for comprehensive oversight and management of all USSD applications within the platform.</p>
            <div class="text-center-sm">
                <a href="javascript:void(0);" data-popup-trigger="#request-demo" class="button inline-block">Request a Demo</a>
            </div>
        </div>
        <div class="banner-inner subscription-section">
            <img src="./images/products/lg/ussdhub-mockup.png" width="100%" alt="Grey Parrot IO Cash Management">
        </div>
    </section>

    <!-- <section class="content-group group-section">
        <h3 class="header">Customer Success Stories</h3>
    </section> -->

    <!-- <section class="content-group no-padding-top">
        <div class="tabs">
            <button class="tab-btn active" data-tab="tab1">Stanbic Bank Zambia</button>
            <button class="tab-btn" data-tab="tab2">Armaguard Security Ltd</button>
            <button class="tab-btn" data-tab="tab3">Brompton Securities</button>
        </div>
          
        <div class="tab-content active" id="tab1">
            <blockquote class="quote">
                <h4><q>Figma ipsum component variant main layer. Component arrange figma outline bullet line vector inspect</q></h4>
                <h4 class="text-bold">Figma ipsum component variant</h4>
                <a href="/case-studies" class="link flex no-shrink"><span>Read Case Study</span><img src="./images/arrow-red.png" loading="lazy" alt="Grey Parrot"></a>
            </blockquote>
        </div>
        <div class="tab-content" id="tab2">
            <blockquote class="quote">
                <h4><q>Figma ipsum component variant main layer. Component arrange figma outline bullet line vector inspect</q></h4>
                <h4 class="text-bold">Figma ipsum component variant</h4>
                <a href="/case-studies" class="link flex no-shrink"><span>Read Case Study</span><img src="./images/arrow-red.png" loading="lazy" alt="Grey Parrot"></a>
            </blockquote>
        </div>
        <div class="tab-content" id="tab3">
            <blockquote class="quote">
                <h4><q>Figma ipsum component variant main layer. Component arrange figma outline bullet line vector inspect</q></h4>
                <h4 class="text-bold">Figma ipsum component variant</h4>
                <a href="/case-studies" class="link flex no-shrink"><span>Read Case Study</span><img src="./images/arrow-red.png" loading="lazy" alt="Grey Parrot"></a>
            </blockquote>
        </div>
    </section> -->

    <section class="content-group group-section">
        <h3 class="header">Frequently Asked Questions</h3>
    </section>

    <section class="content-group no-padding-top margin-bottom-huge">
        <section class="faq-section">
            <details>
                <summary>
                    <b class="text-bold">Can I use the no-code platform to build my USSD application?</b>
                    <span class="closed"><img src="./images/plus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                    <span class="opened"><img src="./images/minus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                </summary>
                <p>Absolutely! Our no-code platform lets you build any USSD application you can think of. No coding skills needed - just drag, drop, and create.</p>
            </details>
        
            <details>
                <summary>
                    <b class="text-bold">Can the platform be customized to meet our specific needs?</b>
                    <span class="closed"><img src="./images/plus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                    <span class="opened"><img src="./images/minus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                </summary>
                <p>Yes, we offer customization options to ensure our product meets your specific needs. We're happy to discuss how we can tailor our product to your requirements. Our team is committed to flexibility and customer satisfaction, so feel free to let us know about any special requests you may have.</p>
            </details>
        
            <details>
                <summary>
                    <b class="text-bold">Is your platform cloud-based or on-premises?</b>
                    <span class="closed"><img src="./images/plus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                    <span class="opened"><img src="./images/minus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                </summary>
                <p>Our platform is primarily designed for cloud-based deployment. However, we offer on-premise options to accommodate client preferences. You can choose the deployment model that best fits your organization's needs and infrastructure requirements.</p>
            </details>
        </section>
    </section>

    <section class="content-group banner-section">
        <div class="banner-inner info-section">
            <h2>Request Your Free Demo NOW</h2>
            <p>Create and launch USSD applications effortlessly with our no-code platform. Request a demo now to see how you can design, deploy, and manage USSD services without any coding experience!</p>
        </div>
        <form class="banner-inner subscription-section" action="javascript:void(0);" id="request-demo-2-form">
            <div class="form-group">
                <div class="alert alert-success d-none" id="alert-success-2">Thank you! Your form has been successfully submitted. Our team will review your information and get back to you shortly</div>
                <div class="alert alert-danger d-none" id="alert-danger-2">We're sorry, but there was an issue submitting your form. Please try again later or contact our support team if the problem persists.</div>
                <label for="full-name">Name</label>
                <div class="form-row">
                    <input type="text" name="full-name" id="full-name" class="max-width-auto" required>
                    <input type="hidden" name="product" id="product" readonly value="ussdhub" class="max-width-auto">
                </div>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <div class="form-row">
                    <input type="email" name="email" id="email" class="max-width-auto" required>
                </div>
            </div>
            <div class="form-group">
                <button class="button" type="submit" id="request-demo-2-submit-button">Submit Request</button>
            </div>
        </form>
    </section>
  </article>

  <Modal product="ussdhub" />

  <script slot="custom-script" src="/js/tab.js"></script>
  <script slot="custom-script" src="/js/popup.js"></script>
  <script slot="custom-script" src="/js/product-demo-form.js"></script>
</BaseLayout>