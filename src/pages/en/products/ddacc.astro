---
import "../../../styles/main.css";
import Navbar from "../../../components/Navbar.astro";
import Hero from "../../../components/products/Hero.astro";
import ProblemSolution from "../../../components/products/ProblemSolution.astro";
import Benefits from "../../../components/products/Benefits.astro";
import Testimonials from "../../../components/products/Testimonials.astro";
import KeyFeatures from "../../../components/products/KeyFeatures.astro";
import CtaStrip from "../../../components/products/CtaStrip.astro";
import WhiteFaq from "../../../components/products/WhiteFaq.astro";
import { getLangFromUrl } from "../../../utils/i18n";
import { ddaccData } from "../../../data/products/ddacc.js";

const locale = getLangFromUrl(Astro.url);
---

<!doctype html>
<html lang={locale}>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GP DDACC - Direct Debit & Credit Automation | Grey Parrot</title>
    <!-- Preload critical fonts -->
    <link rel="preload" href="/fonts/Raleway-Regular.woff2" as="font" type="font/woff2" crossorigin />
    <link rel="preload" href="/fonts/Raleway-Medium.woff2" as="font" type="font/woff2" crossorigin />
    <link rel="preload" href="/fonts/Raleway-Bold.woff2" as="font" type="font/woff2" crossorigin />

    <!-- Font loading optimization -->
    <style>
      /* Font fallback to prevent layout shift */
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
      }

      /* Add font-display: block for critical elements to prevent FOUT */
      h1, h2, .hero-text-content h1, .hero-text-content p, .button {
        font-display: block;
      }

      /* Ensure consistent text sizing */
      html {
        text-size-adjust: 100%;
        -webkit-text-size-adjust: 100%;
      }

      /* Apply custom fonts only after they're loaded */
      html.fonts-loaded body {
        font-family: "Raleway", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      }

      /* Ensure strong tags are always bold */
      strong {
        font-weight: 700 !important;
      }

      /* Extra emphasis for product name */
      .extra-bold {
        font-family: "Raleway", sans-serif !important;
        font-weight: 900 !important;
        color: #241F20 !important;
      }

      html.fonts-loaded h1,
      html.fonts-loaded h2,
      html.fonts-loaded .hero-text-content h1 {
        font-family: "Raleway", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        font-weight: 700;
      }
    </style>
  </head>
  <body>
    <!-- Navbar Component -->
    <Navbar locale={locale} />

    <main>
      <!-- Hero Component -->
      <Hero
          title={ddaccData.hero.title}
          description={ddaccData.hero.description}
          imagePath={ddaccData.hero.imagePath}
          imageAlt={ddaccData.hero.imageAlt}
          ctaPrimary={ddaccData.hero.ctaPrimary}
          ctaSecondary={ddaccData.hero.ctaSecondary}
      />

      <ProblemSolution
          videoId={ddaccData.problemSolution.videoId}
          videoTitle={ddaccData.problemSolution.videoTitle}
          problemTitle={ddaccData.problemSolution.problemTitle}
          introText={ddaccData.problemSolution.introText}
          problemList={ddaccData.problemSolution.problemList}
          solutionTitle={ddaccData.problemSolution.solutionTitle}
          solutionText={ddaccData.problemSolution.solutionText}
      />

      <Benefits
          heading={ddaccData.benefits.heading}
          benefits={ddaccData.benefits.benefits}
      />

      <Testimonials
          heading={ddaccData.testimonials.heading}
          testimonials={ddaccData.testimonials.testimonials}
      />

      <KeyFeatures
          heading={ddaccData.keyFeatures.heading}
          features={ddaccData.keyFeatures.features}
      />

      <CtaStrip
          heading={ddaccData.cta.heading}
          subheading={ddaccData.cta.subheading}
          primaryButtonText={ddaccData.cta.primaryButtonText}
          secondaryButtonText={ddaccData.cta.secondaryButtonText}
      />

      <WhiteFaq
          heading={ddaccData.faq.heading}
          faqItems={ddaccData.faq.faqItems}
      />
    </main>

    <script is:inline src="/js/popup.js"></script>
    <script is:inline src="/js/main.js"></script>

    <!-- Font loading script -->
    <script is:inline>
      // This script helps with font loading and reduces FOUT (Flash of Unstyled Text)
      if ("fonts" in document) {
        // Preload critical fonts
        Promise.all([
          document.fonts.load("400 1em Raleway"),
          document.fonts.load("500 1em Raleway"), // Medium weight for buttons
          document.fonts.load("700 1em Raleway")
        ]).then(() => {
          document.documentElement.classList.add('fonts-loaded');
        });
      } else {
        // Fallback for browsers that don't support the Font Loading API
        document.documentElement.classList.add('fonts-loaded');
      }
    </script>
  </body>
</html>
