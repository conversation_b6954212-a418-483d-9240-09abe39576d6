---
import "../../../styles/main.css";
import Navbar from "../../../components/Navbar.astro";
import Hero from "../../../components/products/Hero.astro";
import ProblemSolution from "../../../components/products/ProblemSolution.astro";
import Benefits from "../../../components/products/Benefits.astro";
import Testimonials from "../../../components/products/Testimonials.astro";
import KeyFeatures from "../../../components/products/KeyFeatures.astro";
import CtaStrip from "../../../components/products/CtaStrip.astro";
import WhiteFaq from "../../../components/products/WhiteFaq.astro";
import { getLangFromUrl } from "../../../utils/i18n";

const locale = getLangFromUrl(Astro.url);
---

<!doctype html>
<html lang={locale}>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Product Template</title>
    <!-- Preload critical fonts -->
    <link rel="preload" href="/fonts/Raleway-Regular.woff2" as="font" type="font/woff2" crossorigin />
    <link rel="preload" href="/fonts/Raleway-Medium.woff2" as="font" type="font/woff2" crossorigin />
    <link rel="preload" href="/fonts/Raleway-Bold.woff2" as="font" type="font/woff2" crossorigin />

    <!-- Font loading optimization -->
    <style>
      /* Font fallback to prevent layout shift */
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
      }

      /* Add font-display: block for critical elements to prevent FOUT */
      h1, h2, .hero-text-content h1, .hero-text-content p, .button {
        font-display: block;
      }

      /* Ensure consistent text sizing */
      html {
        text-size-adjust: 100%;
        -webkit-text-size-adjust: 100%;
      }

      /* Apply custom fonts only after they're loaded */
      html.fonts-loaded body {
        font-family: "Raleway", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      }

      /* Ensure strong tags are always bold */
      strong {
        font-weight: 700 !important;
      }

      /* Extra emphasis for product name */
      .extra-bold {
        font-family: "Raleway-Black", sans-serif !important;
        font-weight: 900 !important;
        color: #241F20 !important;
      }

      html.fonts-loaded h1,
      html.fonts-loaded h2,
      html.fonts-loaded .hero-text-content h1 {
        font-family: "Raleway-Bold", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      }
    </style>
  </head>
  <body>
    <!-- Navbar Component -->
    <Navbar locale={locale} />

    <main>
      <!-- Hero Component -->
      <Hero
          title="Eliminate Payment Failures & Operational Delays in Direct Debit & Credit Processes"
          description="Automate payments, reduce fraud risks and ensure full regulatory compliance with our automated clearing solution <span class='extra-bold'>GP DDACC</span>, trusted by leading banks"
          imagePath="/images/products/happy_customer_image.png"
          imageAlt="GP DDACC Happy Customer"
          ctaPrimary="Talk to an Expert"
          ctaSecondary="See how GP DDACC works"
      />

      <ProblemSolution
          videoId="dQw4w9WgXcQ"
          videoTitle="GP DDACC Solution Video"
          problemTitle="Direct Debit & Credit Clearing Challenges"
          introText="Failing to address DDAC inefficiencies can lead to customer distrust, high operational costs, fraud risks, and lost competitiveness in modern payments."
          problemList={[
              "Manual processes causing delays and errors",
              "High fraud vulnerability due to weak verification",
              "Increased compliance risks and regulatory penalties",
              "Customer dissatisfaction from failed or delayed payments"
          ]}
          solutionTitle="Introducing GP DDACC"
          solutionText="Our automated solution eliminates inefficiencies, reduces fraud, and ensures compliance—trusted by leading banks."
      />

      <Benefits
          heading="Why Choose GP DDACC?"
          benefits={[
              {
                  title: "Seamless Payment Automation",
                  description: "Reduce manual interventions with our AI-driven transaction processing",
                  iconSrc: "/images/products/ddac_benefits_icon_1.svg",
                  iconAlt: "Automation Icon"
              },
              {
                  title: "Enhanced Fraud Detection",
                  description: "Advanced fraud detection mechanisms keep transactions secure",
                  iconSrc: "/images/products/ddac_benefits_icon_2.svg",
                  iconAlt: "Security Icon"
              },
              {
                  title: "Regulatory Compliance",
                  description: "Built-in-compliance checks ensure adherence to banking regulations",
                  iconSrc: "/images/products/ddac_benefits_icon_3.svg",
                  iconAlt: "Compliance Icon"
              }
          ]}
      />

      <Testimonials
          heading="What Our Client Say"
          testimonials={[
              {
                  quote: "Lorem ipsum dolor sit amet consectetur adipiscing elit Ut et massa mi. Aliquam in hendrerit urna. Pellentesque sit amet sapien fringilla, mattis ligula consectetur, ultrices mauris. Maecenas vitae mattis tellus.",
                  name: "Michael Kumi",
                  title: "CEO",
                  company: "Good Bank",
                  imageSrc: "https://randomuser.me/api/portraits/men/32.jpg"
              },
              {
                  quote: "Lorem ipsum dolor sit amet consectetur adipiscing elit Ut et massa mi. Aliquam in hendrerit urna. Pellentesque sit amet sapien fringilla, mattis ligula consectetur, ultrices mauris. Maecenas vitae mattis tellus.",
                  name: "Michael Kumi",
                  title: "CEO",
                  company: "Good Bank",
                  imageSrc: "https://randomuser.me/api/portraits/men/32.jpg"
              },
              {
                  quote: "Lorem ipsum dolor sit amet consectetur adipiscing elit Ut et massa mi. Aliquam in hendrerit urna. Pellentesque sit amet sapien fringilla, mattis ligula consectetur, ultrices mauris. Maecenas vitae mattis tellus.",
                  name: "Michael Kumi",
                  title: "CEO",
                  company: "Good Bank",
                  imageSrc: "/images/products/testimonial-avatar.jpg"
              }
          ]}
      />

      <KeyFeatures
          heading="Key Features"
          features={[
              {
                  title: "Real Time Monitoring & Alerts",
                  description: "Get instant notifications on payment failures, successful debits and regulatory updates"
              },
              {
                  title: "Scalability & Customization",
                  description: "Our solution scales with your bank's needs, allowing custom workflows and business rules"
              },
              {
                  title: "Seamless Bank Integration",
                  description: "We ensure smooth integrations with core banking, payment gateways and compliance systems"
              },
              {
                  title: "Automated Reconciliation",
                  description: "Ensure accurate and fast transaction reconciliation without manual effort"
              },
              {
                  title: "Account Sniffing Module",
                  description: "Maximize collection activities with real-time account status checks and automated retries"
              },
              {
                  title: "Fraud Prevention & Compliance",
                  description: "Protect your business with advanced fraud detection and ensure regulatory compliance"
              }
          ]}
      />

      <CtaStrip
          heading="Ready to Experience Seamless Collections?"
          subheading="See how GP DDACC can transform your bank's direct debit operations"
          primaryButtonText="Talk to an Expert"
          secondaryButtonText="See how GP DDACC works"
      />

      <WhiteFaq
          heading="Frequently Asked Questions"
          faqItems={[
              {
                  question: "What is GP DDACC?",
                  answer: "GP DDACC is a direct debit automation platform that simplifies collections for banks",
                  isOpen: true
              },
              {
                  question: "How does GP DDACC work?",
                  answer: "GP DDACC integrates with your core banking system to automate direct debit processes, monitor transactions in real-time, and provide comprehensive reporting."
              },
              {
                  question: "What banks can use GP DDACC?",
                  answer: "GP DDACC is designed for all banks that offer direct debit services"
              },
              {
                  question: "Can I integrate GP DDACC with my core banking system?",
                  answer: "Yes, GP DDACC is designed to integrate seamlessly with all major core banking systems through our secure API infrastructure."
              }
          ]}
      />
    </main>

    <script is:inline src="/js/popup.js"></script>
    <script is:inline src="/js/main.js"></script>

    <!-- Font loading script -->
    <script is:inline>
      // This script helps with font loading and reduces FOUT (Flash of Unstyled Text)
      if ("fonts" in document) {
        // Preload critical fonts
        Promise.all([
          document.fonts.load("1em Raleway"),
          document.fonts.load("500 1em Raleway"), // Medium weight for buttons
          document.fonts.load("700 1em Raleway-Bold")
        ]).then(() => {
          document.documentElement.classList.add('fonts-loaded');
        });
      } else {
        // Fallback for browsers that don't support the Font Loading API
        document.documentElement.classList.add('fonts-loaded');
      }
    </script>
  </body>
</html>