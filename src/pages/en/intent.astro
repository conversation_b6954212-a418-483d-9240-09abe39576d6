---
---

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Redirecting...</title>
</head>
<body>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const urlParams = new URLSearchParams(window.location.search);
      const intentUrl = urlParams.get('url');

      if (intentUrl) {
        window.location.replace(intentUrl);
      } else {
        document.body.innerHTML = 'Invalid intent link. Please check the URL and try again.';
      }
    });
  </script>
</body>
</html>