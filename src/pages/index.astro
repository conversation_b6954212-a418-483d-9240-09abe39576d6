---
// This is a static redirect page that works without JavaScript
// We'll use a meta refresh tag for the redirect
export const prerender = true;

// Get supported languages from our i18n config
const supportedLangs = ['en', 'fr'];
const defaultLang = 'en';

// This will be used to generate static redirects
export function getStaticPaths() {
  return [
    // Root path redirects to default language
    {
      params: {},
      props: { targetLang: defaultLang }
    },
    // Support direct language paths (e.g., /en, /fr)
    ...supportedLangs.map(lang => ({
      params: { lang },
      props: { targetLang: lang }
    }))
  ];
}

// Get the target language from props
const { targetLang } = Astro.props;

// Set the title based on the target language
const pageTitle = `Redirecting to ${targetLang.toUpperCase()} version...`;
---

<!doctype html>
<html lang={targetLang}>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{pageTitle}</title>
    
    <!-- Primary meta refresh -->
    <meta http-equiv="refresh" content={`0;url=/${targetLang}/`} />
    
    <!-- Fallback for browsers that don't support meta refresh -->
    <script is:inline>
      // Simple redirect that works even if meta refresh is blocked
      setTimeout(() => {
        window.location.href = `/${targetLang}/`;
      }, 100);
    </script>
    <style>
      body {
        font-family: 'Raleway', sans-serif;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
        background-color: #f8f9fa;
        color: #241F20;
      }

      p {
        text-align: center;
        font-size: 1.2rem;
      }

      a {
        color: #ff0000;
        text-decoration: none;
      }

      a:hover {
        text-decoration: underline;
      }
    </style>
  </head>
  <body>
    <div style="text-align: center; padding: 2rem; font-family: system-ui, sans-serif;">
      <h1>Redirecting to {targetLang} version...</h1>
      <p>If you are not redirected automatically, please click the button below:</p>
      <a 
        href={`/${targetLang}/`} 
        style="
          display: inline-block;
          background: #3182ce;
          color: white;
          padding: 0.75rem 1.5rem;
          border-radius: 0.375rem;
          text-decoration: none;
          font-weight: 500;
          margin-top: 1rem;
        "
      >
        Go to {targetLang.toUpperCase()} Version
      </a>
    </div>
  </body>
</html>
