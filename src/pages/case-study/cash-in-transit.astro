---
import CaseModal from "../../components/CaseModal.astro";
import BaseLayout from "../../layouts/BaseLayout.astro";
---

<!-- Main Content -->
<BaseLayout title="Case Study">
    <article class="content">

        <!-- Hero Banner -->

        <section class="content-group hero-banner">
            <div class="wrapper">
                <div class="hero-banner-inner text-area">
                    <a href="/case-studies" class="link margin-bottom-large"><img src="./images/arrow-red.png" alt="Grey Parrot" class="flip">Case Studies</a>
                    <h2 class="margin-bottom-large">Unlocking Financial Agility: How GP Cash-In-Transit Instant Credit Revolutionized One Company's Cash Flow Management</h2>
                </div>
                <div class="hero-banner-inner img-area">
                    <img src="./images/case-studies/cash-in-transit.png" alt="Grey Parrot Case Studies">
                </div>
            </div>
        </section>

        <section class="content-group executive-section">
            <section>
                <h2 class="header">Executive Summary</h2>
                <p>In a mere six months of implementation, our innovative platform generated an impressive <b class="text-bold">ZMW 224 million</b> in real-time value for Cash-in-Transit (CIT) clients. This remarkable achievement was realized while onboarding only 10% of the total targeted pickup sites, showcasing the platform's immense potential for scalability and value creation. The streamlined onboarding process, taking just 5 minutes per client, allows for swift integration and immediate value realization following the first CIT consignment pickup.</p>
            </section>
            <section class="fade">
                <h2 class="header">Client Background</h2>
                <p>Stanbic Bank Zambia, a member of the Standard Bank Group, the largest bank in Zambia by balance sheet, is one of the major engines driving the Zambian economy. Stanbic Bank Zambia prides itself on having 26 branches, four private banking suites, and 99 ATMs amongst others.</p>
            </section>
            <!-- <section>
                <h2 class="header">Read more</h2>
            </section> -->
            <!-- <section>
                <h2 class="header">Problem Statement</h2>
                <p>Figma ipsum component variant main layer. Text object arrange rectangle hand boolean effect follower selection link. Rotate star arrange figjam share list asset mask. Move duplicate bullet flows layout scale invite background reesizing horizontal. Opacity export font ellipse hand. Align create star mask arrow rectangle distribute outline shadow shadow. Group effect pen vertical link library vector flows horizontal overflow. Figma mask connection image blur component team. Prototype object prototype component variant line. Layer ipsum clip auto connection vector. Pixel rectangle rotate pencil effect group boolean. Follower list background link figma export align export comment opacity. Variant outline library rotate reesizing clip line device draft bold. List plugin scale bullet line style slice bullet. Ellipse boolean move arrow text create vector background opacity.</p>
                <p>Text asset figma selection move list underline style. Italic fill star group clip effect ellipse layer. Mask library distribute arrow style figjam. Blur scrolling undo effect main scrolling overflow vector fill. Prototype stroke clip vertical ellipse select horizontal. Export edit arrow image stroke fill. Layer text layout connection invite union background team. Layout pen image connection editor team distribute duplicate vector. Blur pencil strikethrough select mask overflow line.</p>
                <p>Prototype inspect inspect select fill draft device. Slice scale draft shadow align rotate team plugin. Scrolling fill variant bullet vector component device slice library project. Hand editor inspect layout italic inspect. Distribute flows pixel inspect community bold layer. Bullet layer undo vertical connection star share arrow export list. Boolean flows font team device pixel share group comment device. Union share select hand reesizing underline select. Effect flatten ipsum fill hand share scrolling edit outline polygon. Blur pixel connection union arrange pixel select scale shadow plugin.</p>
                <p>Team prototype figjam scrolling distribute edit. Invite line pen community pixel selection object distribute select scrolling. Pencil style bullet hand project undo subtract. Italic slice line prototype selection follower slice overflow figjam. Flows community scale auto team comment bold editor. Content project project scrolling effect overflow move link device. List frame edit background layout fill. Follower outline move outline follower scrolling draft pixel. Blur font layout create background mask text vertical. Font hand horizontal effect connection draft comment invite underline thumbnail. Scrolling polygon bullet flows fill figjam.</p>
            </section>
            <section>
                <h2 class="header">Solution</h2>
                <p>Lorem ipsum dolor sit amet consectetur. Nam duis curabitur sed a sed. Mus lectus ac velit in consequat. Laoreet arcu interdum lobortis varius ornare consequat amet. Praesent velit neque tellus duis nunc ultrices lobortis odio. Vitae at nulla orci tortor adipiscing. Pellentesque convallis felis duis mi. Non cursus phasellus accumsan arcu pharetra. Tortor mi aenean consequat turpis. Ut at lacinia aliquam viverra. Justo sed tempus diam nec nunc accumsan donec. In lorem ut nulla tellus et bibendum at rhoncus sed. Vel adipiscing blandit elementum in ut. Commodo justo faucibus sit aliquam aliquet tincidunt in.</p>
            </section>
            <section>
                <h2 class="header">Implementation Process</h2>
                <p>Lorem ipsum dolor sit amet consectetur. Nam duis curabitur sed a sed. Mus lectus ac velit in consequat. Laoreet arcu interdum lobortis varius ornare consequat amet. Praesent velit neque tellus duis nunc ultrices lobortis odio. Vitae at nulla orci tortor adipiscing. Pellentesque convallis felis duis mi. Non cursus phasellus accumsan arcu pharetra. Tortor mi aenean consequat turpis. Ut at lacinia aliquam viverra. Justo sed tempus diam nec nunc accumsan donec. In lorem ut nulla tellus et bibendum at rhoncus sed. Vel adipiscing blandit elementum in ut. Commodo justo faucibus sit aliquam aliquet tincidunt in.</p>
            </section>
            <section>
                <h2 class="header">Results</h2>
                <p>Figma ipsum component variant main layer. Text object arrange rectangle hand boolean effect follower selection link. Rotate star arrange figjam share list asset mask. Move duplicate bullet flows layout scale invite background reesizing horizontal. Opacity export font ellipse hand. Align create star mask arrow rectangle distribute outline shadow shadow. Group effect pen vertical link library vector flows horizontal overflow. Figma mask connection image blur component team. Prototype object prototype component variant line. Layer ipsum clip auto connection vector. Pixel rectangle rotate pencil effect group boolean. Follower list background link figma export align export comment opacity. Variant outline library rotate reesizing clip line device draft bold. List plugin scale bullet line style slice bullet. Ellipse boolean move arrow text create vector background opacity.</p>
                <blockquote>
                    <h3><q>Figma ipsum component variant main layer. Component arrange figma outline bullet line vector inspect</q></h3>
                    <h3 class="text-bold">Figma ipsum component variant</h3>
                </blockquote>
            </section>
            <section>
                <h2 class="header">What's Next</h2>
                <p>Lorem ipsum dolor sit amet consectetur. Nam duis curabitur sed a sed. Mus lectus ac velit in consequat. Laoreet arcu interdum lobortis varius ornare consequat amet. Praesent velit neque tellus duis nunc ultrices lobortis odio. Vitae at nulla orci tortor adipiscing. Pellentesque convallis felis duis mi. Non cursus phasellus accumsan arcu pharetra. Tortor mi aenean consequat turpis. Ut at lacinia aliquam viverra. Justo sed tempus diam nec nunc accumsan donec. In lorem ut nulla tellus et bibendum at rhoncus sed. Vel adipiscing blandit elementum in ut. Commodo justo faucibus sit aliquam aliquet tincidunt in.</p>
            </section> -->
        </section>

        <section class="content-group group-section no-padding-top margin-bottom-huge">
            <a href="javascript:void(0);" data-popup-trigger="#case-demo" class="button">Read more</a>
        </section>
        <!-- Services -->
        <!-- <section class="content-group group-section">
            <h3 class="text-bold">Recommended Case Studies</h3>
        </section> -->
        <!-- <section class="content-group case-section">
            <div class="card">
                <div class="card-image">
                    <img src="./images/case-studies/cases/stanbic-logo.png" alt="Grey Parrot Software Development" loading="lazy">
                </div>
                <div class="card-body">
                    <h4>Unlocking Financial Agility: How Cash-In-Transit Instant Credit Revolutionized One Company's Cash Flow Management</h4>
                    <div class="card-footer"><a href="/case-study/cash-in-transit" class="link">More<img src="./images/arrow-red.png" alt="Grey Parrot"></a></div>
                </div>
            </div>
            <div class="card">
                <div class="card-image">
                    <img src="./images/placeholder.png" alt="Grey Parrot Software Development" loading="lazy">
                </div>
                <div class="card-body">
                    <h4>Lorem ipsum dolor sit amet consectetur. Vel odio aliquet</h4>
                    <div class="card-footer"><a href="#" class="link">More<img src="./images/arrow-red.png" alt="Grey Parrot"></a></div>
                </div>
            </div>
            <div class="card">
                <div class="card-image">
                    <img src="./images/placeholder.png" alt="Grey Parrot Software Development" loading="lazy">
                </div>
                <div class="card-body">
                    <h4>Lorem ipsum dolor sit amet consectetur. Vel odio aliquet</h4>
                    <div class="card-footer"><a href="#" class="link">More<img src="./images/arrow-red.png" alt="Grey Parrot"></a></div>
                </div>
            </div>
        </section> -->

        <!-- Newsletter -->
        <!-- <section class="content-group banner-section">
            <div class="banner-inner info-section">
                <h2>Are you an innovator?</h2>
                <h2>Interested in how you can win at your digital transformation initiatives?</h2>
                <p>Not convinced? See our last <a href="#" class="link inline-block">update</a> to our subscribers as well as our <a href="#" class="link inline-block">privacy policy</a>.</p>
            </div>
            <div class="banner-inner subscription-section">
                <h5>Subscribe to our Newsletter</h5>
                <div class="form-group">
                    <label for="email">Email</label>
                    <div class="form-row">
                        <input type="email" name="email" id="email">
                        <button class="button">Subscribe</button>
                    </div>
                </div>
            </div>
        </section> -->
        
    </article>

    <CaseModal title="CIT" />
    
    <script slot="custom-script" src="/js/popup.js"></script>
</BaseLayout>