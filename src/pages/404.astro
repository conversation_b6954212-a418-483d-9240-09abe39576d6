---
// This file handles 404 errors and redirects to the default locale if needed
---

<script>
  // Check if we're at the root and redirect to default locale
  if (window.location.pathname === '/' || window.location.pathname === '') {
    window.location.href = "/en";
  }
</script>

<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Page Not Found</title>
    <!-- Using JavaScript redirection instead of meta refresh -->
  </head>
  <body>
    <div class="container">
      <h1>404 - Page Not Found</h1>
      <p>The page you are looking for might have been removed or is temporarily unavailable.</p>
      <p>Redirecting to <a href="/en">homepage</a>...</p>
    </div>
  </body>
</html>

<style>
  body {
    font-family: 'Raleway', sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
    background-color: #f8f9fa;
    color: #241F20;
  }

  .container {
    text-align: center;
    padding: 2rem;
    max-width: 600px;
  }

  h1 {
    color: #ff0000;
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  p {
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  a {
    color: #ff0000;
    text-decoration: none;
  }

  a:hover {
    text-decoration: underline;
  }
</style>
