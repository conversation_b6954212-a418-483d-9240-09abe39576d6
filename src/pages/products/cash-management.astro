---
import BaseLayout from "../../layouts/BaseLayout.astro";
import Modal from "../../components/Modal.astro";
---

<BaseLayout title="Cash Management">
  <!-- Hero Component -->
  <section class="hero page">
    <div class="hero-main">
        <div class="hero-main-text text-center">
            <h1>Efficient Cash Handling</h1>
            <p>Automate manual cash management tasks, integrate seamlessly with existing systems, and ensure efficient and rigorous cash handling in cash-centric economies.</p>
            <div class="flex wrap">
                <a href="javascript:void(0);" data-popup-trigger="#request-demo" class="button margin-right no-shrink">Request a Demo</a>
                <a href="/case-studies" class="link flex no-shrink"><span>See Case Study</span><img src="./images/arrow-red.png" loading="lazy" alt="Grey Parrot"></a>
            </div>
        </div>
    </div>
  </section>

  <!-- Main Content -->
  <article class="content product-page">

    <!-- Hero Banner -->
    <section class="content-group hero-banner">
        <img src="./images/products/lg/cash-management.png" loading="lazy" class="product-page-image" alt="Grey Parrot IO Cash Management">
    </section>

    <!-- Services -->
    <section class="content-group product-description-section">
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/money-receive-circle.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Real-Time Cash Receipt</h5>
                <p>Optimize your cash flow with expedited credit to your account upon cash pickup. Eliminate reconciliation delays and leverage your funds promptly for strategic business needs.</p>
            </div>
        </div>
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/square-lock-check.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Robust Authorization Framework</h5>
                <p>Our system implements stringent authorization protocols, requiring dual approval for critical actions such as cash delivery or pickup requests, cash tracking, and receipt acknowledgment. This safeguards your funds and maintains operational integrity.</p>
            </div>
        </div>
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/checkmark-circle.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Automated Reconciliation</h5>
                <p>Our platform streamlines reconciliation processes for various accounts, including transit, teller, cash center, vault, and central bank accounts. By integrating with your core banking system, we eliminate manual errors and ensure accurate debit/credit transactions.</p>
            </div>
        </div>
        <div class="card">
            <div class="card-image product-card-header">
                <img src="./images/coins.png" loading="lazy" alt="Grey Parrot IO Products">
            </div>
            <div class="card-body">
                <h5>Comprehensive Cash Management Solution</h5>
                <p>Centralized cash management operations, streamlining requests, tracking, and reconciliation. With robust security measures, automated processes, and seamless integration, we optimize cash flow and mitigate risks. </p>
            </div>
        </div>
    </section>
    
    <section class="content-group banner-section">
        <div class="banner-inner info-section">
            <h1>Instant Credit, Zero Hassle</h1>
            <p class="margin-bottom-large">Experience seamless, instant credit approval with fully automated processing—no paperwork, no waiting, just quick access to the funds you need.</p>

            <br>
            <h1>Robust Issue Resolution</h1>
            <p class="margin-bottom-large">Our platform effectively handles various issues, including fake notes, cash shortages, incorrect crediting, and transaction reversals. We prioritize customer preferences for issue resolution, ensuring a tailored approach to address each concern.</p>
            <div class="text-center-sm">
                <a href="javascript:void(0);" data-popup-trigger="#request-demo" class="button inline-block">Request a Demo</a>
            </div>
        </div>
        <div class="banner-inner subscription-section">
            <img src="./images/products/lg/cash-management-mockup.png" width="100%" alt="Grey Parrot IO Cash Management">
        </div>
    </section>

    <!-- <section class="content-group group-section">
        <h3 class="header">Customer Success Stories</h3>
    </section> -->

    <!-- <section class="content-group no-padding-top">
        <div class="tabs">
            <button class="tab-btn active" data-tab="tab1">Stanbic Bank Zambia</button>
            <button class="tab-btn" data-tab="tab2">Armaguard Security Ltd</button>
            <button class="tab-btn" data-tab="tab3">Brompton Securities</button>
        </div>
          
        <div class="tab-content active" id="tab1">
            <blockquote class="quote">
                <h4><q>Figma ipsum component variant main layer. Component arrange figma outline bullet line vector inspect</q></h4>
                <h4 class="text-bold">Figma ipsum component variant</h4>
                <a href="/case-studies" class="link flex no-shrink"><span>Read Case Study</span><img src="./images/arrow-red.png" loading="lazy" alt="Grey Parrot"></a>
            </blockquote>
        </div>
        <div class="tab-content" id="tab2">
            <blockquote class="quote">
                <h4><q>Figma ipsum component variant main layer. Component arrange figma outline bullet line vector inspect</q></h4>
                <h4 class="text-bold">Figma ipsum component variant</h4>
                <a href="/case-studies" class="link flex no-shrink"><span>Read Case Study</span><img src="./images/arrow-red.png" loading="lazy" alt="Grey Parrot"></a>
            </blockquote>
        </div>
        <div class="tab-content" id="tab3">
            <blockquote class="quote">
                <h4><q>Figma ipsum component variant main layer. Component arrange figma outline bullet line vector inspect</q></h4>
                <h4 class="text-bold">Figma ipsum component variant</h4>
                <a href="/case-studies" class="link flex no-shrink"><span>Read Case Study</span><img src="./images/arrow-red.png" loading="lazy" alt="Grey Parrot"></a>
            </blockquote>
        </div>
    </section> -->

    <section class="content-group group-section">
        <h3 class="header">Frequently Asked Questions</h3>
    </section>

    <section class="content-group no-padding-top margin-bottom-huge">
        <section class="faq-section">
            <details>
                <summary>
                    <b class="text-bold">What is CIT?</b>
                    <span class="closed"><img src="./images/plus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                    <span class="opened"><img src="./images/minus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                </summary>
                <p>CIT (Cash in Transit) is a specialized solution within our platform that offers secure management of cash transportation and instant credit value for all cash pickups.</p>
            </details>
        
            <details>
                <summary>
                    <b class="text-bold">Is your platform cloud-based or on-premises?</b>
                    <span class="closed"><img src="./images/plus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                    <span class="opened"><img src="./images/minus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                </summary>
                <p>Our platform is primarily designed for on-premise deployment. However, we offer cloud-ready options to accommodate client preferences. You can choose the deployment model that best fits your organization's needs and infrastructure requirements.</p>
            </details>
        
            <details>
                <summary>
                    <b class="text-bold">Can your solution integrate with our existing banking systems and processes?</b>
                    <span class="closed"><img src="./images/plus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                    <span class="opened"><img src="./images/minus-sign.png" loading="lazy" alt="Grey Parrot IO FAQ's"></span>
                </summary>
                <ul>
                    <span>Yes, our platform is designed for seamless integration with:</span>
                    <ul>
                        <li>Existing business processes</li>
                        <li>Core banking systems</li>
                        <li>Reconciliation systems</li>
                    </ul>
                    <span>
                        This comprehensive integration capability ensures that our platform can work harmoniously with your current infrastructure, enhancing efficiency and maintaining data consistency across all your financial operations.
                    </span>
                </ul>
            </details>
        </section>
    </section>

    <section class="content-group banner-section">
        <div class="banner-inner info-section">
            <h2>Request Your Free Demo NOW</h2>
            <p>Experience unparalleled security and efficiency in cash management with our state-of-the-art Cash-in-Transit solution.</p>
        </div>
        <form class="banner-inner subscription-section" action="javascript:void(0);" id="request-demo-2-form">
            <div class="form-group">
                <div class="alert alert-success d-none" id="alert-success-2">Thank you! Your form has been successfully submitted. Our team will review your information and get back to you shortly</div>
                <div class="alert alert-danger d-none" id="alert-danger-2">We're sorry, but there was an issue submitting your form. Please try again later or contact our support team if the problem persists.</div>
                <label for="full-name">Name</label>
                <div class="form-row">
                    <input type="text" name="full-name" id="full-name" class="max-width-auto" required>
                    <input type="hidden" name="product" readonly id="product" value="cash-management" class="max-width-auto">
                </div>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <div class="form-row">
                    <input type="email" name="email" id="email" class="max-width-auto" required>
                </div>
            </div>
            <div class="form-group">
                <button class="button" type="submit" id="request-demo-2-submit-button">Submit Request</button>
            </div>
        </form>
    </section>
  </article>

  <Modal product="cash-management" />

  <script slot="custom-script" src="/js/tab.js"></script>
  <script slot="custom-script" src="/js/popup.js"></script>
  <script slot="custom-script" src="/js/product-demo-form.js"></script>
</BaseLayout>