/* Popups */
[data-popup] {
  --popup-header: 60px;
  --popup-footer: auto;
  --popup-header-text-color: #241F20;
  --popup-header-background-color: transparent;
  --popup-max-width: 600px;
  --popup-max-height: 600px;
  --popup-z-index: 1000000;
  z-index: calc(-1 * var(--popup-z-index));
  position: fixed;
}


[data-popup].show {
  z-index: var(--popup-z-index);
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #00000050;
  display: flex;
  justify-content: center;
}

[data-popup]:not(.show),
[data-popup]:not(.show) * {
  z-index: calc(-1 * var(--popup-z-index));
  height: 0;
  opacity: 0;
}

.popup {
  width: 50vw;
  max-width: var(--popup-max-width);
  background-color: #fff;
  position: relative;
  height: max-content;
  min-height: 100px;
  max-height: var(--popup-max-height);
  top: min(calc(50% - (var(--popup-max-height) / 2)), 200px);
  border-radius: .25rem;
  overflow: hidden;
}

.popup .popup-header {
  height: var(--popup-header);
  display: flex;
  align-items: center;
  background-color: var(--popup-header-background-color);
  color: var(--popup-header-text-color);
  padding-inline: 1.5rem;
  padding-top: 1.5rem;
  font-weight: bolder;
  justify-content: space-between;
}

.popup .popup-header img {
    width: 24px;
}

.popup .popup-footer {
  height: var(--popup-footer);
  display: flex;
  align-items: center;
  justify-content: end;
  padding-inline: 1rem;
}

.popup .popup-content {
  padding: 2rem;
  overflow: hidden scroll;
  height: calc(var(--popup-max-height) - var(--popup-header) - var(--popup-footer)) !important;
}

@media screen and (max-width: 810px), screen and (max-height: 600px) {
  [data-popup] {
    --popup-max-width: 100vw;
    --popup-max-height: 100%;
  }

  [data-popup].show .popup {
    max-width: 100vw;
    width: 100vw;
    max-height: 100vh;
    height: 100vh;
  }
}