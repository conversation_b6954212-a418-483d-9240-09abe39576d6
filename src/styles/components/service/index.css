.service-section {
    display: grid;
    grid-template-rows: auto auto;
    grid-template-areas: "first-card second-card"
                        "full-card full-card";
    gap: 2rem;
    max-width: var(--max-content-width);
    margin-inline: auto;
    padding-top: 0;
}

.service-section .card:nth-child(1) {
    grid-area: first-card;
}

.service-section .card:nth-child(2) {
    grid-area: second-card;
}

.service-section .card:nth-child(3) {
    grid-area: full-card;
}

.service-section .card {
    background-color: #F9F9F9;
    border-radius: .25rem;
    display: flex;
    flex-direction: column;
    max-width: calc(var(--max-content-width) / 2);
}

.service-section .card.row {
    flex-direction: row;
    max-width: 100%;
    width: 100%;
    gap: 2rem;
    align-items: center;
}

.service-section .card.row .card-image,
.service-section .card.row .card-body {
    width: 50%;
}

.service-section .card.row .card-image {
    height: 100%;
}

.service-section .card.row .card-image img {
    height: 100%;
    max-height: 500px;
}

.service-section .card .card-body {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.service-section .card .card-image img {
    width: 100%;
    object-fit: cover;
}

.service-section .card .card-body h3 {
    font-weight: bolder;
}

.service-section .card .card-body p {
    line-height: 1.4;
}

.service-section .card .card-body em {
    font-style: italic;
}

.service-section .card .card-body .card-footer {
    color: var(--danger-clr);
    display: flex;
    align-items: center;
    gap: .5rem;
}

@media screen and (max-width: 768px) {
    .service-section {
        grid-template-areas: "first-card"
                            "second-card"
                            "full-card";
    }

    .service-section .card.row {
        flex-direction: column;
    }

    .service-section .card .card-body .card-footer {
        flex-direction: column;
        align-items: flex-start;
    }

    .service-section .card:nth-child(3) .card-image {
        order: -1;
    }

    .service-section .card.row .card-image,
    .service-section .card.row .card-body {
        width: 100%;
    }

    .service-section .card.row {
        gap: 0;
    }
}

@media screen and (min-width: 768px) and (max-width: 1100px) {
    .service-section .card .card-body .card-footer {
        flex-direction: column;
        align-items: flex-start;
    }
}