.hero {
    --animation-main-clr: #0F4BAF;
    --animation-secondary: #0F4BAF54;
    min-height: calc(100vh - var(--app-navbar-height));
    height: 100%;
    background-color: var(--hero-bg-clr);
    display: flex;
    flex-direction: column;
    padding: 3rem 2rem;
    color: #fff;
    position: relative;
    overflow: hidden;
}

.hero.page {
    background-color: #fff;
    background-image: url("../../images/backgrounds/background.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    min-height: calc(80vh - var(--app-navbar-height));
    color: #000;
    padding-top: 2.5rem;
}

.hero.page.no-bg {
    background-image: none;
    background-color: #fff;
    min-height: max-content;
    padding-bottom: 0;
}

.hero.page.no-bg .hero-main {
    margin-bottom: 0;
}

.hero.page .hero-main {
    margin-bottom: 15vh;
}

.hero.page .hero-main p {
    max-width: 80ch;
    margin-inline: auto;
}

.hero.page .hero-main h1 {
    max-width: 30ch !important;
}

.hero .hero-main {
    display: flex;
    align-items: center;
    padding: 4vw 4vw;
    max-width: var(--max-content-width);
    margin-inline: auto;
    gap: 2rem;
}

.hero .button {
    z-index: 10;
    position: relative;
}

.hero .hero-foot {
    --client-width: 200px;
    --duration: 20s;
    max-width: calc(var(--client-width) * 5);
    width: 100%;
    margin-inline: auto;
    overflow: hidden;
}

.hero-foot h4 {
    text-align: center;
    font-weight: bold;
    margin-bottom: 3rem;
    margin-top: 2rem;
}

.hero-foot .clients {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0;
    flex-wrap: nowrap;
    width: 100%;
    animation: scroll var(--duration) linear infinite;
}

@media (prefers-reduced-motion: reduce) {
    .hero-foot .clients {
      animation-play-state: paused;
    }
}

.hero-foot .clients img {
    width: var(--client-width);
    padding-inline: 1rem;
}

@keyframes scroll {
    0% {
      transform: translateX(0);
    }
  
    100% {
      transform: translateX(-100%);
    }
}

.hero-main-text h1 {
    font-family: "Raleway-Bold";
    font-size: min(4rem, 4vw);
}

.hero-main-text p {
    margin: 2rem 0;
    line-height: 1.6;
}

.hero-main-animation {
    min-width: 40%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-main-animation::after {
    content: "";
    position: absolute;
    background-color: var(--hero-blue-clr);
    opacity: .5;
    width: 650px;
    height: 1200px;
    transform: rotate(-220deg);
    top: -30%;
    left: 20%;
    border-radius: 20rem;
    filter: blur(200px);
    z-index: -1;
}

.animation-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
}

.animation-container .bar {
    display: flex;
    align-items: center;
    gap: 1rem;
    background-color: var(--animation-main-clr);
    padding: 1rem;
    border-radius: 20px;
    width: 250px;
    justify-content: center;
    transform: translateZ(0);
    will-change: transform, width, background-color;
}

.animation-container .bar:first-child,
.animation-container .bar:last-child {
    background-color: transparent;
    background: none;
    opacity: .3;
}

.animation-container .bar:nth-child(2),
.animation-container .bar:nth-child(4) {
    background-color: var(--animation-secondary);
}

.animation-container .bar:nth-child(3) {
    width: 350px;
}

.animation-container .bar img {
    width: 20px;
}

.animation-container .bar span {
    font-family: "Raleway-Bold";
    font-size: 1.25rem;
}

/* Animation */
.animation-container .bar:nth-child(3) {
    width: 350px;
    animation: slideout 1s cubic-bezier(0.25, 1, 0.5, 1) infinite;
    animation-delay: 400ms;
}

.animation-container .bar:nth-child(4) {
    animation: slidein 1s cubic-bezier(0.25, 1, 0.5, 1) infinite;
    animation-delay: 400ms;
    opacity: .5;
}

.animation-container .bar:nth-child(2) {
    animation: slideaway 1s cubic-bezier(0.25, 1, 0.5, 1) infinite;
    animation-delay: 400ms;
    opacity: .5;
}

.animation-container .bar:nth-child(1) {
    animation: slidedown 1s cubic-bezier(0.25, 1, 0.5, 1) infinite;
    animation-delay: 400ms;
}

.animation-container .bar:nth-child(5) {
    animation: slideup 1s cubic-bezier(0.25, 1, 0.5, 1) infinite;
    animation-delay: 400ms;
    opacity: .3;
}

@keyframes slideout {
    100% {
        opacity: .5;
    }
    to {
        transform: translateY(calc(-100% - 1.5rem));
        width: 250px;
        background-color: var(--animation-secondary);
    }
}

@keyframes slidein {
    100% {
        opacity: 1;
    }
    to {
        transform: translateY(calc(-100% - 1.5rem));
        width: 350px;
        background-color: var(--animation-main-clr);
    }
}

@keyframes slideaway {
    100% {
        opacity: .3;
    }
    to {
        transform: translateY(calc(-100% - 1.5rem));
        width: max-content;
        min-width: 250px;
        background-color: transparent;
    }
}

@keyframes slidedown {
    20% { opacity: 0; }
    70% { opacity: .25; }
    95% { opacity: .3; }
    to {
        transform: translateY(calc((100% * 5) + (1.5rem * 1.7)));
    }
}

@keyframes slideup {
    100% {
        opacity: .5;
    }
    to {
        transform: translateY(calc(-100% - 1.5rem));
        width: 250px;
        background-color: var(--animation-secondary);
    }
}

@media screen and (max-width: 768px) {
    .hero {
        height: 100%;
        min-height: calc(100vh - var(--app-navbar-height));
        padding: 1rem;
        padding-bottom: 6rem;
    }

    .hero .hero-main {
        flex-direction: column;
    }

    .hero-main-text h1 {
        font-size: 2.5rem;
    }

    .hero-main-text h1,
    .hero-main-text div,
    .hero-main-text p {
        text-align: center;
    }


    .hero-foot {
        margin-top: 3rem;
    }

    .hero-foot h3 {
        font-size: 1rem;
    }

    .hero-foot .clients img {
        width: 140px;
    }

    .hero-main-animation {
        margin-top: 2rem;
    }

    .hero-main-animation::after {
        top: -50%;
        opacity: .3;
    }
}

@media screen and (min-height: 1000px) {
    .hero {
        min-height: 100%;
    }
}

@media screen and (max-width: 420px) {
    .hero-main-animation {
        zoom: .75;
    }
}

@media screen and (max-width: 320px) {
    .hero-main-animation {
        zoom: .5;
    }
}