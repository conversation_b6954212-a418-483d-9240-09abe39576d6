footer {
    --footer-dark-clr: #590000; 
    display: flex;
    flex-direction: column;
    gap: 3rem;
    background: #fff;
}

footer .foot {
    padding: 1.5rem;
    background-color: var(--footer-dark-clr);
}

footer .head,
footer .main,
footer .foot {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.head .contact-section {
    width: calc(0.30 * var(--max-content-width));
    display: flex;
    gap: 2rem;
    flex-direction: column;
    padding: 0 2rem;
}

.contact-section > img {
    width: 200px;
}

.contact-section a {
    color: #000;
    text-decoration: none;
}

.contact-section ul li {
    margin-bottom: .5rem;
}

.head .useful-links-section {
    width: calc(0.70 * var(--max-content-width));
}

.useful-links-section {
    display: flex;
    justify-content: space-around;
}

.useful-links-section div {
    display: flex;
    flex-direction: column;
    gap: 2.25rem;
}

.useful-links-section div h5 {
    font-weight: bolder;
    font-family: "Raleway-Bold";
}

.useful-links-section div ul {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.useful-links-section div ul a {
    text-decoration: none;
    color: #000;
}

.useful-links-section div ul a:hover {
    text-decoration: underline;
}

.main {
    margin-bottom: 1rem;
}

.main .useful-links-section {
    gap: 10rem;
}

footer .foot {
    justify-content: space-around;
}

.foot .social-media-links,
.foot .terms-and-policy,
.foot .copyright {
    max-width: calc(var(--max-content-width) / 3);
    width: max-content;
    color: #fff;
    display: flex;
    justify-content: center;
    gap: 1rem;
    align-items: center;
    font-weight: bold;
}

.foot a {
    color: #fff;
    text-decoration: none;
}

.foot a:hover {
    text-decoration: underline;
}

.foot .social-media-links a {
    display: flex;
    justify-content: center;
    align-items: center;
}

@media screen and (max-width: 1200px) {
    footer .foot {
        justify-content: space-between;
    }
}

@media screen and (max-width: 1000px) {
    footer .head {
        flex-direction: column;
        gap: 3rem;
    }

    .useful-links-section {
        justify-content: space-between;
    }

    .head .contact-section,
    .head .useful-links-section,
    .main .useful-links-section {
        width: 100%;
    }

    .head .contact-section {
        padding: 0;
    }

    .head,
    .main {
        padding: 2rem;
    }

    .main {
        padding-bottom: 5rem;
    }

    .main .useful-links-section {
        justify-content: flex-start;
    }

    .contact-section {
        margin-bottom: 3rem;
    }

    footer .foot {
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 2rem;
    }

    .foot .social-media-links,
    .foot .terms-and-policy,
    .foot .copyright {
        width: max-content;
        justify-content: flex-start;
        flex-shrink: 0;
    }

    .foot .terms-and-policy {
        justify-content: center;
    }

    .foot .copyright {
        justify-content: flex-end;
    }
}

@media screen and (max-width: 768px) {
    .useful-links-section {
        flex-direction: column;
        gap: 2rem;
    }

    .main .useful-links-section {
        gap: 2rem;
    }

    footer {
        gap: 0;
    }

    .main {
        padding-top: 0;
    }
}

@media screen and (max-width: 536px) {
    footer .foot {
        justify-content: center;
    }
}