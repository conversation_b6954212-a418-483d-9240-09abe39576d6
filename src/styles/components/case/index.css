.case-section {
    display: flex;
    gap: 2rem;
    max-width: var(--max-content-width);
    margin-inline: auto;
    padding-top: 0;
    margin-bottom: 5rem;
    flex-wrap: wrap;
    justify-content: start;
    width: 100%;
}

.case-section .card {
    background-color: #F9F9F9;
    border-radius: .25rem;
    display: flex;
    flex-direction: column;
    width: calc((100% / 3) - 2rem);
    min-width: 350px;
    flex-shrink: 0;
}


.case-section .card .card-image {
    height: 100%;
}

.case-section .card .card-image img {
    height: 400px;
    max-height: 400px;
}

.case-section .card .card-body {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.case-section .card .card-image img {
    width: 100%;
    object-fit: cover;
    border-radius: .25rem .25rem 0 0;
}

.case-section .card .card-body h4 {
    font-weight: bolder;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 2;
}

.case-section .card .card-body p {
    line-height: 1.4;
}

.case-section .card .card-body em {
    font-style: italic;
}

.case-section .card .card-body .card-footer {
    color: var(--danger-clr);
    display: flex;
    align-items: center;
    gap: .5rem;
}

@media screen and (max-width: 900px) {
    .case-section .card {
        width: 100%;
        min-width: 100%;
    }
}