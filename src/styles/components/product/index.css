.product-demo-section {
    scroll-snap-type: x mandatory;
    overflow-y: scroll;
    scroll-behavior: smooth;
}

.product {
    background-color: #F9F9F9;
    display: flex;
    padding: 2rem 0 0 1rem;
    gap: 2rem;
    max-width: 1000px;
    width: 100%;
    border-radius: .25rem;
    flex-shrink: 0;
    scroll-snap-align: center;
}

.product .product-details h2 {
    font-family: "Raleway-Bold";
    font-size: 2.5rem;
}

.product-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 1rem;
}

.product-details ul {
    margin-left: .65rem;
    display: flex;
    flex-direction: column;
    gap: .65rem;
    list-style-type: disc;
}

.product-details a {
    color: var(--danger-clr);
    font-family: "Raleway-Bold";
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: .5rem;
}

.product-image img {
    width: 100%;
}

.product-details h4 {
    font-weight: bolder;
}

.product-page {
    margin-top: -30vh;
}

.product-page-image {
    background-color: aliceblue;
    width: 100%;
    position: relative;
    margin-bottom: 3rem;
}

.product-description-section {
    --description-gap: 4rem;
    display: flex;
    gap: var(--description-gap);
    max-width: var(--max-content-width);
    margin-inline: auto;
    padding-top: 0;
    margin-bottom: 5rem;
    flex-wrap: wrap;
    justify-content: space-around;
}

.product-description-section .card {
    border-radius: .25rem;
    display: flex;
    flex-direction: column;
    width: calc((100% / 4) - var(--description-gap));
    min-width: 250px;
    flex-shrink: 0;
}

.product-description-section .card {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    color: #241F20;
}

.product-description-section .card .card-body h5 {
    margin-bottom: 1rem;
    font-family: "Raleway-Bold";
}

.product-description-section .card .card-body p {
    line-height: 1.5;
}

.product-card-header {
    width: max-content;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    font-size: 1rem;
    position: relative;
    margin-bottom: 2rem;
}
  
.product-card-header::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 50%;
    border: 2px solid transparent;
    background: linear-gradient(45deg,var(--danger-clr), transparent) border-box;
    -webkit-mask:
       linear-gradient(#fff 0 0) padding-box, 
       linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
            mask-composite: exclude;
}

@media screen and (max-width: 1223px) {
    .product-description-section .card {
        width: calc((100% / 2) - 2rem);
    }
}

@media screen and (min-width: 1223px) {
    .product-description-section {
        --description-gap: 2rem;
    }
}


@media screen and (max-width: 900px) {
    .product-description-section .card {
        width: 100%;
        min-width: 100%;
    }

    .product-details {
        max-width: 100%;
    }
}
  

@media screen and (max-width: 567px) {
    .product-page {
        margin-top: -15vh;
    }
}


@media screen and (max-width: 768px) {
    .product {
        flex-direction: column;
        width: 100%;
        max-width: 100%;
    }
}

@media screen and (min-width: 768px) and (max-width: 1024px) {
    .product {
        width: calc(0.80 * 100vw);
        max-width: calc(0.80 * 100vw);
    }
}

@media screen and (min-width: 1300px) {
    .product {
        max-width: 1200px;
        width: 1200px;
    }

    .product-details {
        max-width: 500px;
    }
}