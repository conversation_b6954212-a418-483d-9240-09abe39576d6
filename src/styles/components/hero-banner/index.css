.hero-banner {
    width: 100%;
    max-width: var(--max-content-width);
    display: flex;
    gap: 6rem;
    justify-content: center;
    align-items: center;
    margin-inline: auto;
}

.hero-banner .wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    border-radius: .5rem;
    background-color: #F4F4F4;
}

.hero-banner-inner {
    --inner-width-main: 60%;
    display: flex;
    padding: max(2rem, 3vw);
}

.hero-banner-inner.img-area {
    justify-content: center;
    align-items: center;
    position: relative;
    width: calc(100% - var(--inner-width-main));
    padding: 0;
}

.hero-banner-inner.img-area img {
    width: 100%;
    border-radius: 0 .25rem .25rem 0;
}

.hero-banner-inner.text-area {
    gap: 2rem;
    flex-direction: column;
    width: var(--inner-width-main);
}

.hero-banner-inner.text-area .header {
    margin-bottom: 0;
}

.hero-banner-inner.text-area h2 {
    font-size: max(1.5rem, 2.5vw);
    line-height: 1.2;
    font-family: "Raleway-Bold";
}


@media screen and (max-width: 768px) {
    .hero-banner .wrapper {
        flex-direction: column;
        align-items: flex-start;
        gap: 2rem;
    }

    .hero-banner-inner.img-area,
    .hero-banner-inner.text-area {
        width: 100%;
    }

    .hero-banner-inner.img-area img {
        position: relative;
        width: 100%;
        max-width: 100%;
    }
}