.navbar {
    --navbar-height: var(--app-navbar-height);
    --navbar-ul-gap: 1rem;
    width: 100%;
    height: var(--navbar-height);
    display: flex;
    align-items: center;
    background-color: #fff;
    position: sticky;
    top: 0;
    z-index: 100;
    max-width: var(--max-page-width);
    margin-inline: auto;
    font-family: "Raleway", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.navbar ul {
    display: flex;
    width: max-content;
    margin-inline: auto;
    gap: 1.5rem;
    align-items: center;
    justify-content: space-between;
}

.navbar ul li a {
    text-decoration: none;
    color: #000;
}

.navbar .logo-container {
    display: inline-block;
}

.navbar .logo {
    width: 150px;
    margin-right: var(--navbar-ul-gap);
}

.navbar .contact-us,
.navbar .contact-us:focus {
    padding: .65rem 1.5rem;
    font-size: 1rem;
    background-color: transparent;
    outline: 0;
    border: 1px solid var(--danger-clr);
    color: var(--danger-clr);
    border-radius: 4px;
    cursor: pointer;
    transition: all .3s ease;
}

.navbar .contact-us {
    margin-left: var(--navbar-ul-gap);
}

.navbar .contact-us:hover {
    background-color: var(--danger-clr);
    color: #fff;
}

.navbar .menu {
    display: none;
    cursor: pointer;
    padding: .75rem .5rem;
}

.navbar .nav-icon {
    width: 1rem;
}

.navbar .dropdown-trigger {
    display: flex;
    align-items: center;
    gap: .5rem;
    cursor: pointer;
}

.navbar .dropdown-trigger img {
    transition: all .3s ease;
}

.navbar .dropdown-content,
.nav-mobile-dropdown-content {
    display: none;
    position: absolute;
    background-color: #fff; 
    padding: .65rem 1rem;
    border-radius: .25rem;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 1000px;
    min-width: 500px;
    border-radius: 0 0 10px 10px;
    padding: 2rem 10rem 4rem 3rem;
    z-index: 100;
    margin-top: 1.5rem;
    gap: 2.5rem;
}

.navbar .dropdown-content.mobile,
.navbar .nav-mobile-dropdown-content {
    padding: 1.5rem 1.5rem 2rem 1.5rem;
    width: 100vw;
    min-width: 100vw;
    height: calc(100vh - var(--app-navbar-height));
    left: 0;
    transform: translateX(0);
    box-sizing: border-box;
    overflow: hidden;
}

.navbar .dropdown-content.mobile#mobile-content {
    background: transparent;
    padding: 0;
}

.navbar .dropdown-content.mobile#mobile-content ul {
    background: #fff;
    padding: 1.5rem 1.5rem 2rem 1.5rem;
    height: max-content;
}

.navbar .dropdown-content#mobile-content {
    position: fixed;
    display: flex;
    left: 200vw;
    transition: all .3s ease;
}

.navbar .dropdown-content#mobile-content.show {
    transition: all .3s ease;
    left: 0;
}

.mobile ul {
    all: unset;
    display: flex;
    flex-direction: column;
    width: max-content;
    gap: 1.5rem;
}

.mobile ul li {
    all: unset !important;
}

.mobile ul li a {
    text-decoration: none;
    display: inline-block;
    padding-block: .25rem;
    width: 100%;
    color: #000;
    cursor: pointer;
}

.mobile ul li button.contact-us {
    width: 100%;
    margin: .5rem 0 0 0;
    background-color: var(--danger-clr);
    color: #fff;
}

.mobile .nav-mobile-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile .nav-mobile-link img {
    transform: rotate(270deg);
}

.nav-mobile-return-link {
    display: flex !important;
    gap: .75rem;
    align-items: center;
    width: max-content;
}

.nav-mobile-return-link img {
    transform: rotate(90deg);
}

.navbar .nav-mobile-dropdown-content {
    position: fixed;
    display: flex;
    padding: 1.5rem;
    margin: 0;
    inset: 0;
    left: 200vw;
    top: 0;
    height: calc(100vh - var(--app-navbar-height));
    flex-direction: column;
    overflow: hidden auto;
    transition: all .3s ease;
}

.navbar .nav-mobile-dropdown-content.show {
    left: 0;
}

.navbar .dropdown-content section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.navbar .dropdown-content section h6 {
    font-weight: 700;
    color: #BBBABA;
    font-family: "Raleway";
}

.navbar .dropdown-link p {
    margin-top: .5rem;
    color: #707070;
    font-size: .9rem;
}

.navbar .dropdown-link a {
    font-family: "Raleway";
    font-weight: 700;
    color: #000;
    text-decoration: none;
}

.navbar .dropdown-link a:hover {
    text-decoration: underline;
}

/* Dropdown Logic */
.navbar .dropdown-content.desktop {
    opacity: 0;
    transition: all .3s ease;
    z-index: -10000;
}

.navbar .dropdown-content.desktop.show {
    opacity: 1;
    z-index: 100;
    position: absolute;
    display: flex;
}

.navbar .active img {
    transform: rotate(180deg);
}

.navbar .normal-weight {
    font-family: "Raleway" !important;
}

@media screen and (max-width: 810px) {
    .navbar .menu {
        display: block;
    }

    .navbar ul {
        width: 100%;
        margin: 0 1rem 0 1rem;
    }

    .mobile ul {
        margin: 0;
    }

    .navbar ul li:not(:first-child) {
        display: none;
    }

    .navbar ul li:last-child {
        display: block;
    }

    .navbar .dropdown-content,
    .nav-mobile-dropdown-content {
        margin-top: 0;
        top: var(--app-navbar-height);
    }

    .navbar .nav-mobile-dropdown-content {
        padding-bottom: 5rem;
    }
}

@media screen and (min-width: 810px) and (max-width: 980px) {
    .call-number {
        display: none;
    }
}

@media screen and (min-width: 1040px) {
    .navbar {
        --navbar-ul-gap: 3rem;
    }
}