/* Fonts */
@font-face {
  font-family: "Raleway";
  src: url("/fonts/Raleway-Regular.woff2") format("woff2"),
       url("/fonts/Raleway-Regular.ttf") format("truetype");
  font-weight: 400;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Raleway";
  src: url("/fonts/Raleway-Medium.woff2") format("woff2"),
       url("/fonts/Raleway-Medium.ttf") format("truetype");
  font-weight: 500;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Raleway";
  src: url("/fonts/Raleway-Bold.woff2") format("woff2"),
       url("/fonts/Raleway-Bold.ttf") format("truetype");
  font-weight: 700;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Raleway";
  src: url("/fonts/Raleway-Black.woff2") format("woff2"),
       url("/fonts/Raleway-Black.ttf") format("truetype");
  font-weight: 900;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Montserrat";
  src: url("/fonts/Montserrat-SemiBold.woff2") format("woff2"),
       url("/fonts/Montserrat-SemiBold.ttf") format("truetype");
  font-weight: 600;
  font-display: swap;
  font-style: normal;
}

/* Resets */
*, ::after, ::before {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Eric Meyer's Reset CSS */
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* Modernizr's CSS Reset */
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
  display: block;
}

ul, ol {
  list-style: none;
}

/* Variables */
:root {
  --font-family-regular: Raleway, sans-serif;
  --font-size: 16px;
  --default-text-color: #000;
  --default-background-color: #262626;

  --danger-clr: #FF0000;
  --hero-bg-clr: #262626;
  --hero-blue-clr: #1063F1;
  --app-navbar-height: 75px;

  --max-content-width: 1500px;
  --max-page-width: 2000px;
}


/* Custom Resets */
html {
  width: 100%;
  height: 100vh;
  font-size: var(--font-size);
  font-family: var(--font-family-regular);
  color: var(--default-text-color);
  scroll-behavior: smooth;
}

html body {
  background-color: var(--default-background-color);
}

h1 {
  font-size: 2rem;
}

h2 {
  font-size: 1.5rem;
}

h3 {
  font-size: 1.25rem;
}

h4 {
  font-size: 1.125rem;
}

h5 {
  font-size: 1rem;
}

h6 {
  font-size: 0.875rem;
}

em {
  font-style: italic;
}

/* Utils */
.see {
  border: 2px solid red !important;
}

.ff-montserrat-sb {
  font-family: "Montserrat-SemiBold", sans-serif;
}

.text-danger {
  color: var(--danger-clr) !important;
}

.text-center {
  text-align: center !important;
}

.flex {
  display: flex !important;
  justify-content: center;
  align-items: center;
  gap: .5rem;
}

.flex-start {
  justify-content: start;
}

.wrap {
  flex-wrap: wrap;
}

.no-shrink {
  flex-shrink: 0;
}

.margin-right {
  margin-right: .75rem;
}

.max-width-500 {
  max-width: 500px;
}

.max-width-auto {
  max-width: 100%;
}

.width-100 {
  width: 100% !important;
}

.no-padding-top {
  padding-top: 0 !important;
}

.no-padding {
  padding: 0 !important;
}

.quote {
  background-color: #F6F6F6;
  /* max-width: calc(var(--max-content-width) / 2); */
  width: 100%;
  padding: 3rem;
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 1rem;
  font-size: 1rem;
}

.text-muted {
  color: #BBBABA;
}

.no-scroll {
  touch-action: none !important;
}

.margin-bottom-large {
  margin-bottom: 2rem !important;
}

.margin-bottom-extra {
  margin-bottom: 4rem !important;
}

.margin-bottom-huge {
  margin-bottom: 8rem !important;
}

.padding {
  padding: 2rem !important;
}

.font-small {
  font-size: 0.9rem;
}

.no-margin-bottom {
  margin-bottom: 0 !important;
}

.text-bold {
  font-weight: bold;
}

.inline-block {
  display: inline-block !important;
}

.d-none {
  display: none !important;
}

.pointer {
  cursor: pointer !important;
}

.fade {
  position: relative;
}

.fade::after {
  width: 110%;
  height: 40%;
  position: absolute;
  bottom: 0;
  left: -5%;
  background-color: #fff;
  filter: blur(10px);
  content: "";
}

.alert {
  font-size: .9rem;
  font-weight: bold;
  border: 1px solid;
  padding: .75rem 1rem;
  border-radius: .25rem;
  color: #4A4949;
  line-height: 1.4;
}

.alert.alert-success {
  background-color: #B8FBCF;
  border-color:#08B50E;
}

.alert.alert-danger {
  background-color: #FBD3D3;
  border-color:#FA9090;
}

a.link {
  color: var(--danger-clr);
  font-family: "Raleway-Bold";
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: .5rem;
  width: max-content;
}

a.link:hover {
  text-decoration: underline;
}

.margin-bottom-half {
  margin-bottom: .5rem !important;
}

img.flip {
  transform: rotate(180deg);
}

.header {
  min-width: 180px;
  width: max-content;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: .6rem 2rem;
  font-size: 1rem;
  font-family: "Raleway-Bold";
  color: var(--danger-clr);
  position: relative;
  margin-bottom: 2rem;
}

.header::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 30px;
  border: 2px solid transparent;
  background: linear-gradient(45deg,var(--danger-clr), transparent) border-box;
  -webkit-mask:
     linear-gradient(#fff 0 0) padding-box, 
     linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
          mask-composite: exclude;
}

.button {
  padding: .65rem 1.5rem;
  font-size: 1rem;
  background-color: var(--danger-clr);
  color: #fff;
  outline: 0;
  border: 1px solid var(--danger-clr);
  border-radius: 4px;
  cursor: pointer;
  transition: all .3s ease;
  display: inline-block;
  text-decoration: none;
  font-weight: bolder;
  font-family: "Raleway";
}

.button.outline {
  background-color: transparent;
  border: 1px solid var(--danger-clr);
  color: var(--danger-clr);
  transition: all .3s ease;
}

.button.outline:hover {
  background-color: var(--danger-clr);
  color: #fff;
}

.large-text {
  font-size: max(2rem, 2vw);
  font-weight: bolder;
  line-height: 1.4;
}

.form-group {
  width: 100%;
  flex-direction: column;
  gap: .5rem;
}

.form-group,
.form-row,
.radio-group {
  display: flex;
}

.form-row {
  gap: 1rem;
}

.radio-group {
  justify-content: flex-start;
  gap: .5rem;
}

.radio-group label {
  font-size: 1rem;
  order: 1;
}

input,
select,
textarea {
  width: 100%;
  padding: .85rem 1rem;
  border: 1px solid #BBBABA;
  border-radius: .25rem;
  outline: 0;
  max-width: 500px;
  font-size: 1rem;
  font-family: "Raleway";
  background-color: transparent;
}

input:focus {
  border: 1px solid #999999;
}

select[disabled] {
  background-color: #E9E9E9;
  color: #BBBABA;
}

label {
  font-size: .85rem;
}

/* Page */
main {
  max-width: var(--max-page-width);
  margin-inline: auto;
}

.content {
  --content-outer-space: 4rem;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  overflow: hidden;
}

.content .content-group {
  padding: calc(var(--content-outer-space) + 1.5rem) var(--content-outer-space) calc(var(--content-outer-space) - 1.5rem);
}

.content .product-section {
  max-width: var(--max-content-width);
  margin-inline: auto;
}

.product-section-header {
  display: flex;
  gap: 10rem;
  align-items: flex-start;
}

.product-controls {
  display: flex;
}

.product-controls button {
  background: transparent;
  border: 0;
  padding: .5rem 1rem;
  margin-right: .5rem;
  cursor: pointer;
  border-radius: .25rem;
  transition: all .3s ease;
}

.product-controls button:hover {
  background-color: #00000005;
}

.product-controls button img {
  width: 18px;
}

.product-controls button img.right {
  transform: rotate(180deg);
}

.content .product-demo-section {
  /* padding: 0 0 0 calc(var(--content-outer-space) + 5rem); */
  display: flex;
  gap: 2rem;
  flex-wrap: nowrap;
  width: 100%;
  flex-direction: row;
  overflow: auto hidden;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.content .product-demo-section::-webkit-scrollbar {
  display: none;
}

.content .group-section {
  display: flex;
  gap: 1rem;
  flex-direction: column;
  align-items: center;
}

.content .group-section p {
  max-width: 1000px;
  text-align: center;
  margin-bottom: 2rem;
}

.world-map {
  background-image: url("../../images/backgrounds/world-map.png");
  background-repeat: no-repeat;
  background-position: 100% 100%;
}

/* Info Row */
.info-row {
  --spacing: 5rem;
  display: flex;
  align-items: center;
}

.info-row > *:first-child {
  width: 40%;
}

.info-row > *:last-child {
  width: 60%;
}


.info-row .order-first {
  order: -1;
}

.info-row .large {
  width: 70% !important;
}

.info-row .small {
  width: 30% !important;
}

.info-row .row-image {
  padding: var(--spacing);
  display: flex;
  justify-content: center;
  align-items: center;
}

.info-row .row-image.show-sm {
  display: none;
}

.info-row .row-image img {
  width: 80%;
}

.info-row .large.row-image img {
  width: 100%;
}

.info-row .small > div {
  min-width: 500px;
  background-color: #fff;
  z-index: 1;
  padding: 3rem 3rem 4rem 0;
  border-radius: .25rem;
}

.info-row .row-details {
  padding: var(--spacing);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
  max-width: 700px;
}

.info-row .row-details h3 {
  font-family: "Raleway-Bold";
}

.info-row .row-details p {
  line-height: 1.5;
}


/* Clients */

.clients {
  display: flex;
  align-items: center;
  justify-content: center;
  width: max-content;
  margin-inline: auto;
  gap: 2rem;
  flex-wrap: wrap;
  width: 100%;
}

.clients img {
  width: 180px;
}

.content-group.partner-section {
  padding: calc(var(--content-outer-space) + 1.5rem) var(--content-outer-space) calc(var(--content-outer-space) + 1.5rem);
}

/* Executive Summary */
.executive-section {
  max-width: var(--max-content-width);
  margin-inline: auto;
  padding-left: 10rem;
}

.executive-section section:not(:first-child) {
  margin-top: 2rem;
}

.executive-section section {
  margin-left: 3rem;
  display: flex;
  flex-direction: column;
  gap: .5rem;
  text-align: left;
  line-height: 1.3;
  padding-bottom: 2rem;
  border-bottom: 2px solid #E9E9E9;
}

.executive-section section blockquote {
  background-color: #F6F6F6;
  max-width: calc(var(--max-content-width) / 2);
  margin-inline: auto;
  margin-block: 2rem;
  width: 100%;
  padding: 2rem;
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 1rem;
}

.executive-section section p:not(:first-child) {
  margin-top: 1rem;
}

.executive-section section:last-child {
  border-bottom: none;
}

.executive-section section hr {
  color: #E9E9E9;
  margin-block: 2rem;
}

.disabled,
.loading,
.loading-content {
  pointer-events: none;
  opacity: 0.5;
}

.loading::after {
  content: "";
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid currentColor;
  border-top: 2px solid transparent;
  border-radius: 50%;
  margin-left: 10px;
  animation: spin 0.8s linear infinite;
}

/* Tab */
.tabs,
.tab-content {
  max-width: 900px;
  margin-inline: auto;
}

.tabs {
  display: flex;
  justify-content: space-between;
  overflow-y: auto;
  scrollbar-width: none;
}

.tabs::-webkit-scrollbar {
  display: none;
}

.tab-btn {
  background-color: transparent;
  padding: .5rem;
  font-size: 1rem;
  border: 0;
  font-family: "Raleway";
  min-width: 200px;
  border-bottom: 3px solid transparent;
  cursor: pointer;
}

.tab-btn.active {
  border-bottom: 3px solid var(--danger-clr);
}

.tab-content {
  display: none;
  padding: 3rem 1rem;
}

.tab-content.active {
  display: block;
}

.tab-btn.active {
  font-weight: bold;
}

/* FAQ Section */
.faq-section {
  max-width: 900px;
  margin-inline: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.faq-section details {
  border-bottom: 1px solid #BBBABA;
}

.faq-section details .closed {
  display: inline-block;
}

.faq-section details .opened {
  display: none;
}

.faq-section details[open] .opened {
  display: inline-block;
}

.faq-section details[open] .closed {
  display: none;
}

.faq-section details .closed img,
.faq-section details .opened img {
  width: 24px;
}


.faq-section details summary + * {
  padding: 0 1rem 1.5rem 1rem;
  line-height: 1.5;
}

.faq-section details ul {
  list-style-type: disc;
}

.faq-section details ul li {
  margin-left: 1.5rem;
}

.faq-section summary {
  list-style: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem 1rem;
  font-size: 1.1rem;
  gap: 1rem;
  cursor: pointer;
}

.faq-section summary b {
  font-family: "Raleway-Bold";
}

.faq-section summary::-webkit-details-marker {
  display: none;
}

.faq-section summary::marker {
  display: none;
}

/* Company page */

.max-width {
  max-width: var(--max-content-width);
  margin-inline: auto;
}

@media screen and (max-width: 568px) {
  .info-row .row-image.show-sm {
    display: flex;
  }
  .info-row .row-image.show-md {
    display: none;
  }

  .world-map > .info-row:last-child .row-details {
    order: 1 !important;
  }

  .tab-content {
    padding: 2rem 0;
  }

  .quote {
    padding: 1.5rem;
  }

  .faq-section summary {
    font-size: 1rem;
  }
}

@media screen and (max-width: 768px) {
  .content {
    --content-outer-space: 2rem;
  }

  .product-section-header {
    flex-direction: column;
    gap: 1.5rem;
  }

  .product-controls button img {
    width: 10px;
  }

  .large-text {
    font-size: 1.1rem;
    font-family: "Raleway-Bold";
  }

  /* .content .product-demo-section {
    padding-left: 2rem;
  } */

  /* Info Row */
  .info-row {
    --spacing: 1rem;
    flex-direction: column;
    margin-bottom: 2rem;
  }

  .info-row:last-child {
    margin-bottom: 0;
  }

  .info-row .row-image img {
    width: 100%;
  }

  .world-map > .info-row:first-child > *:last-child,
  .world-map > .info-row:last-child > *:first-child {
    order: -1;
  }

  .info-row .row-details {
    text-align: center;
  }

  .info-row a.link {
    margin-inline: auto;
  }

  .info-row .row-image {
    width: 90%;
  }

  .info-row .large {
    width: 100% !important;
    padding: 0;
  }
  
  .info-row .small {
    width: 100% !important;
    padding: 0;
    text-align: left;
  }

  .info-row .small > div {
    min-width: 100%;
    padding: 0 0 3rem 0;
  }

  .info-row .row-details.mission {
    padding: 0;
    text-align: left;
  }

  .info-row .row-details.mission ~ .row-image {
    width: 100%;
    margin-top: 2rem;
  }

  /* Clients */
  .clients img {
    width: 140px;
  }

  /* Utils */
  .form-row {
    flex-direction: column;
  }

  .form-row input,
  .form-row select {
    max-width: 100%;
  }

  /* Executive Summary */
  .executive-section section {
    margin-left: 0;
  }
}

@media screen and (min-width: 768px) and (max-width: 1024px) {
  .product-section-header {
    gap: 4rem;
  }

  .product-controls button img {
    width: 14px;
  }
  
  .large-text {
    font-size: 1.5rem;
  }

  /* Info Row */
  .info-row {
    --spacing: 2rem;
  }
}

@media screen and (min-width: 1400px) {  
  .large-text {
    font-size: 2.5rem;
  }
}

@media screen and (max-width: 1500px) {
  /* .content .product-demo-section {
    padding: 0 0 0 var(--content-outer-space);
  } */

  .info-row {
    --spacing: 1.5rem;
  }
}

/* Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}