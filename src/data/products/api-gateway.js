// API Gateway Product Data
export const apiGatewayData = {
  hero: {
    title: "Accelerate Innovation with Secure Banking API Gateway",
    description: "Connect, integrate, and innovate with our powerful <span class='extra-bold'>GP API Gateway</span>, enabling seamless integration between your core banking systems and third-party services",
    imagePath: "/images/products/api_gateway_hero.png",
    imageAlt: "GP API Gateway Platform",
    ctaPrimary: "Request API Documentation",
    ctaSecondary: "Schedule a Demo"
  },

  problemSolution: {
    videoId: "dQw4w9WgXcQ",
    videoTitle: "GP API Gateway Solution Video",
    problemTitle: "API Integration Challenges",
    introText: "Banks face significant hurdles when trying to integrate their legacy systems with modern fintech solutions and third-party services.",
    problemList: [
      "Complex integration with legacy core banking systems",
      "Security vulnerabilities in API connections",
      "Lack of standardization across different systems",
      "Performance bottlenecks during high transaction volumes",
      "Difficulty maintaining compliance with evolving regulations"
    ],
    solutionTitle: "Introducing GP API Gateway",
    solutionText: "Our comprehensive API Gateway provides a secure, scalable, and standardized interface between your core banking systems and external services, enabling rapid innovation while maintaining security and compliance."
  },

  benefits: {
    heading: "Benefits of GP API Gateway",
    benefits: [
      {
        title: "Accelerated Innovation",
        description: "Reduce time-to-market for new services from months to weeks with pre-built connectors and standardized interfaces",
        iconSrc: "/images/products/ddac_benefits_icon_1.svg"
      },
      {
        title: "Enhanced Security",
        description: "Protect sensitive data with advanced encryption, authentication, and comprehensive access controls",
        iconSrc: "/images/products/ddac_benefits_icon_2.svg"
      },
      {
        title: "Simplified Compliance",
        description: "Built-in compliance features for PSD2, Open Banking, GDPR, and other regulatory requirements",
        iconSrc: "/images/products/ddac_benefits_icon_3.svg"
      }
    ]
  },

  testimonials: {
    heading: "What Our Clients Say",
    testimonials: [
      {
        quote: "GP API Gateway has transformed our ability to partner with fintech companies. We've launched five new services in just six months.",
        name: "David Rodriguez",
        title: "CTO",
        company: "Innovation Bank"
      },
      {
        quote: "The security features of the API Gateway give us confidence to open our systems to third-party developers without compromising customer data.",
        name: "Lisa Chen",
        title: "Head of Digital Transformation",
        company: "Global Finance"
      }
    ]
  },

  keyFeatures: {
    heading: "Key Features of GP API Gateway",
    features: [
      {
        title: "Pre-built Connectors",
        description: "Ready-to-use connectors for major core banking systems and popular fintech services"
      },
      {
        title: "Developer Portal",
        description: "Comprehensive documentation, sandbox environment, and testing tools for developers"
      },
      {
        title: "Traffic Management",
        description: "Advanced rate limiting, load balancing, and caching to ensure optimal performance"
      },
      {
        title: "Security Suite",
        description: "Multi-layer security with OAuth 2.0, JWT, encryption, and anomaly detection"
      },
      {
        title: "Analytics Dashboard",
        description: "Real-time monitoring of API usage, performance metrics, and developer activity"
      },
      {
        title: "Compliance Framework",
        description: "Tools for managing consent, data privacy, and regulatory reporting requirements"
      }
    ]
  },

  cta: {
    heading: "Ready to Accelerate Your Digital Transformation?",
    subheading: "See how GP API Gateway can help you innovate faster and more securely",
    primaryButtonText: "Request API Documentation",
    secondaryButtonText: "Schedule a Demo"
  },

  faq: {
    heading: "Frequently Asked Questions",
    faqItems: [
      {
        question: "What is GP API Gateway?",
        answer: "GP API Gateway is a secure, scalable platform that enables banks to easily connect their core systems with third-party services and fintech solutions through standardized APIs.",
        isOpen: true
      },
      {
        question: "How does GP API Gateway ensure security?",
        answer: "Our gateway implements multiple security layers including OAuth 2.0, JWT authentication, encryption, IP filtering, rate limiting, and real-time threat monitoring to protect your systems and data."
      },
      {
        question: "Can GP API Gateway integrate with our legacy systems?",
        answer: "Yes, our platform includes pre-built connectors for most major core banking systems and can be customized to work with proprietary or legacy systems through our adapter framework."
      },
      {
        question: "Does GP API Gateway support Open Banking standards?",
        answer: "Yes, our gateway fully supports Open Banking standards including PSD2 in Europe, the UK Open Banking Standard, and other regional open banking initiatives with built-in compliance features."
      }
    ]
  }
};
