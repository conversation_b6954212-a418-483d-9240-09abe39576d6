---
// src/layouts/ProductLayout.astro
import "../styles/main.css";
import Navbar from "../components/Navbar.astro";
import Hero from "../components/products/Hero.astro";
import ProblemSolution from "../components/products/ProblemSolution.astro";
import Benefits from "../components/products/Benefits.astro";
import Testimonials from "../components/products/Testimonials.astro";
import KeyFeatures from "../components/products/KeyFeatures.astro";
import CtaStrip from "../components/products/CtaStrip.astro";
import WhiteFaq from "../components/products/WhiteFaq.astro";
import { getLangFromUrl } from "../utils/i18n";

// Get product data from props
const {
  title,
  heroData,
  problemSolutionData,
  benefitsData,
  testimonialsData,
  keyFeaturesData,
  ctaData,
  faqData
} = Astro.props;

const locale = getLangFromUrl(Astro.url);
---

<!doctype html>
<html lang={locale}>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{title}</title>
    <!-- Preload critical fonts -->
    <link rel="preload" href="/fonts/Raleway-Regular.woff2" as="font" type="font/woff2" crossorigin />
    <link rel="preload" href="/fonts/Raleway-Medium.woff2" as="font" type="font/woff2" crossorigin />
    <link rel="preload" href="/fonts/Raleway-Bold.woff2" as="font" type="font/woff2" crossorigin />

    <!-- Font loading optimization -->
    <style>
      /* Font fallback to prevent layout shift */
      body {
        font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
      }
      /* Once fonts are loaded, use them */
      .fonts-loaded body {
        font-family: "Raleway", sans-serif;
      }
    </style>
    <slot name="head" />
  </head>
  <body>
    <!-- Navbar Component -->
    <Navbar locale={locale} />

    <main>
      <!-- Hero Component -->
      <Hero
          title={heroData.title}
          description={heroData.description}
          imagePath={heroData.imagePath}
          imageAlt={heroData.imageAlt}
          ctaPrimary={heroData.ctaPrimary}
          ctaSecondary={heroData.ctaSecondary}
      />

      <ProblemSolution
          videoId={problemSolutionData.videoId}
          videoTitle={problemSolutionData.videoTitle}
          problemTitle={problemSolutionData.problemTitle}
          introText={problemSolutionData.introText}
          problemList={problemSolutionData.problemList}
          solutionTitle={problemSolutionData.solutionTitle}
          solutionText={problemSolutionData.solutionText}
      />

      <Benefits
          heading={benefitsData.heading}
          benefits={benefitsData.benefits}
      />

      <Testimonials
          heading={testimonialsData.heading}
          testimonials={testimonialsData.testimonials}
      />

      <KeyFeatures
          heading={keyFeaturesData.heading}
          features={keyFeaturesData.features}
      />

      <CtaStrip
          heading={ctaData.heading}
          subheading={ctaData.subheading}
          primaryButtonText={ctaData.primaryButtonText}
          secondaryButtonText={ctaData.secondaryButtonText}
      />

      <WhiteFaq
          heading={faqData.heading}
          faqItems={faqData.faqItems}
      />

      <slot />
    </main>

    <script is:inline src="/js/popup.js"></script>
    <script is:inline src="/js/main.js"></script>
    <script is:inline>
      // Font loading optimization
      if ("fonts" in document) {
        Promise.all([
          document.fonts.load("1em Raleway"),
          document.fonts.load("500 1em Raleway"),  // Medium weight
          document.fonts.load("700 1em Raleway"),  // Bold weight
          document.fonts.load("900 1em Raleway"),  // Black weight
          document.fonts.load("1em Raleway-Bold"),
          document.fonts.load("1em Raleway-Black"),
          document.fonts.load("1em Montserrat-SemiBold")
        ]).then(() => document.documentElement.classList.add("fonts-loaded"));
      } else {
        document.documentElement.classList.add("fonts-loaded");
      }
    </script>
  </body>
</html>
