---
import "../styles/main.css";
import Navbar from "../components/Navbar.astro";
import Footer from "../components/Footer.astro";
import { getLangFromUrl } from "../utils/i18n";

const { title } = Astro.props;
const locale = getLangFromUrl(Astro.url);
---

<!doctype html>
<html lang={locale}>
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Empowering banks with next-gen software platforms and solutions. Grey Parrot provides secure, user-friendly tools like USSDHub, Cash-In-Transit, and API Gateway to drive growth, enhance customer experience, and support digital transformation. Partner with us to elevate your banking services with custom solutions, quality assurance, and expert resourcing."
    />
    <base href="/" />
    <title>Grey Parrot IO - {title}</title>
  </head>
  <body>
    <Navbar locale={locale} />
    <main>
      <slot />
      <Footer />
    </main>
    <script is:inline src="/js/redirect.js"></script>
    <script is:inline src="/js/main.js"></script>
    <slot name="custom-script" />
    <!-- Microsoft Analytics -->
    <script is:inline type="text/javascript">
      (function (c, l, a, r, i, t, y) {
        c[a] =
          c[a] ||
          function () {
            (c[a].q = c[a].q || []).push(arguments);
          };
        t = l.createElement(r);
        t.async = 1;
        t.src = "https://www.clarity.ms/tag/" + i;
        y = l.getElementsByTagName(r)[0];
        y.parentNode.insertBefore(t, y);
      })(window, document, "clarity", "script", "o0is020tii");
    </script>
  </body>
</html>
