---
const { title } = Astro.props;
---

<div data-popup id="case-demo">
    <div class="popup">
        <section class="popup-header">
            <h3 class="text-bold">Get case study delivered to your inbox</h3>
            <img src="./images/cancel-circle.png" class="pointer" cancel alt="Get Case Study">
        </section>
        <section class="popup-content banner-inner">
            <form action="javascript:void(0);" class="form-section" id="request-case-form">
                <div class="form-group">
                    <div class="alert alert-success d-none" id="alert-success">Thank you! Your form has been successfully submitted. Our team will review your information and get back to you shortly</div>
                    <div class="alert alert-danger d-none" id="alert-danger">We're sorry, but there was an issue submitting your form. Please try again later or contact our support team if the problem persists.</div>
                    <label for="full-name">Name</label>
                    <input type="text" name="full-name" id="full-name" required>
                    <input type="hidden" name="case-study" id="case-study" value={title} readonly>
                </div>
                <div class="form-group">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" name="email" id="email" required>
                        </div>
                        <div class="form-group">
                            <label for="phone-number">Phone Number <em class="text-muted">(optional)</em></label>
                            <input type="tel" name="phone-number" id="phone-number">
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="company-name">Company</label>
                            <input type="text" name="company-name" id="company-name" required>
                        </div>
                        <div class="form-group">
                            <label for="role">Role</label>
                            <input type="text" name="role" id="role" required>
                        </div>
                    </div>
                </div>
            </form>
        </section>
        <section class="popup-footer padding">
            <div class="form-group">
                <button class="button" type="submit" id="request-case-submit-button">Submit Request</button>
                <p class="text-center font-small">By sharing your email, you agree to our <a href="/privacy-policy" class="link inline-block">Privacy Policy</a> and <a href="/terms" class="link inline-block">Terms</a>.</p>
            </div>
        </section>
    </div>
</div>

<script lang="js">
    document.addEventListener("DOMContentLoaded", () => {
        const submitFormButton = document.getElementById("request-case-submit-button")
        const form = document.getElementById("request-case-form")
        const dangerAlert = document.getElementById("alert-danger")
        const successAlert = document.getElementById("alert-success")

        async function sendEmail(data = {}) {
            const DOMAIN = "greyparrot.io";
            const API_KEY = "**************************************************";
            const url = `https://api.mailgun.net/v3/${DOMAIN}/messages`;

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        "Authorization": `Basic ${btoa(`api:${API_KEY}`)}`,
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                    body: data.toString()
                });
                return response;
            } catch (error) {
                console.error('There was a problem with sending email:', error);
                throw error;
            }
        }

        submitFormButton.addEventListener("click", async () => {
            if (!form.reportValidity()) {
                return
            }
            submitFormButton.classList.add("loading")
            form.classList.add("loading-content")

            const formData = new FormData(form)

            const formObject = {};
            formData.forEach((value, key) => {
                formObject[key] = value;
            });

            const requestBody = new URLSearchParams();
            requestBody.append("from", "<EMAIL>");
            requestBody.append("to", formObject?.["email"]);
            requestBody.append("subject", "Request for a demo");
            requestBody.append("template", "website case study");
            requestBody.append("h:X-Mailgun-Variables", JSON.stringify({
                "CUSTOMER_NAME": formObject?.["full-name"],
                "CASE_STUDY": formObject?.["case-study"],
                "CASE_STUDY_LINK": `${formObject?.["case-study"]}-case-study.pdf`
            }));

            // Email for admin
            const requestBody2 = new URLSearchParams();
            requestBody2.append("from", "<EMAIL>");
            requestBody2.append("to", "<EMAIL>");
            requestBody2.append("subject", "Request for a demo");
            requestBody2.append("template", "website case study admin");
            requestBody2.append("h:X-Mailgun-Variables", JSON.stringify({ 
                "COMPANY_NAME": formObject?.["company-name"],
                "CUSTOMER_NAME": formObject?.["full-name"],
                "CUSTOMER_EMAIL": formObject?.["email"],
                "CUSTOMER_PHONE_NUMBER": formObject?.["phone-number"],
                "CUSTOMER_ROLE": formObject?.["role"],
                "CASE_STUDY": formObject?.["case-study"]
            }));


            Promise.all([sendEmail(requestBody), sendEmail(requestBody2)]).then(() => {
                submitFormButton.classList.remove("loading")
                submitFormButton.classList.add("disabled")
                form.classList.remove("loading-content")
                successAlert.classList.remove("d-none")

                // Clear form
                document.getElementById("full-name").value = ""
                document.getElementById("company-name").value = ""
                document.getElementById("email").value = ""
                document.getElementById("phone-number").value = ""
                document.getElementById("role").value = ""
            }).catch((error) => {
                console.error(error)
                submitFormButton.classList.remove("loading")
                dangerAlert.classList.remove("d-none")
                form.classList.remove("loading-content")
            }).finally(() => {
                setTimeout(() => {
                    successAlert.classList.add("d-none")
                    dangerAlert.classList.add("d-none")
                }, 5000)
            })
            
        })
    })
</script>