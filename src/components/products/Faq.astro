---
// Define the properties the component accepts
interface FaqItem {
    question: string;
    answer: string;
    isOpen?: boolean;
}

interface Props {
    heading: string;
    faqItems: FaqItem[];
}

// Get the props passed to the component
const { heading, faqItems } = Astro.props;
---

<section class="faq-section" style="background-color: #ffffff !important;">
    <div class="faq-container">
        <h2>{heading}</h2>
        <div class="faq-list">
            {faqItems.map((item: FaqItem, index: number) => (
                <div class={`faq-item ${item.isOpen ? 'open' : ''}`} data-index={index}>
                    <div class="faq-question">
                        <h3>{item.question}</h3>
                        <button class="toggle-btn" aria-label="Toggle answer">
                            <img src="/images/plus-sign.svg" alt="plus" class="plus" />
                            <img src="/images/minus-sign.svg" alt="minus" class="minus" />
                        </button>
                    </div>
                    <div class="faq-answer">
                        <p>{item.answer}</p>
                    </div>
                </div>
            ))}
        </div>
    </div>
</section>

<style>
    /* Basic variables for easier theme management */
    :root {
        --font-family: 'Raleway', sans-serif;
        --primary-color: #ff0000; /* Primary color */
        --secondary-color: #6c757d; /* Secondary color */
        --text-color: #241F20; /* Dark text */
        --background-color: #f8f9fa; /* Light background */
        --white-color: #ffffff;
        --border-color: #e5e5e5;
        --border-radius: 4px;
        --spacing-unit: 1rem; /* Base unit for padding/margin */
    }

    /* FAQ Section Styles */
    .faq-section {
        background-color: #ffffff !important;
        padding: calc(var(--spacing-unit) * 4) var(--spacing-unit);
        width: 100%;
        box-sizing: border-box;
        position: relative;
        z-index: 10;
        color: var(--text-color);
    }

    /* Override any global styles */
    .faq-section * {
        background-color: #ffffff !important;
    }

    .faq-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100vw;
        height: 100%;
        background-color: #ffffff !important;
        z-index: -1;
    }

    .faq-container {
        max-width: 800px;
        margin: 0 auto;
    }

    /* Heading Styles */
    .faq-container h2 {
        font-family: "Raleway", sans-serif;
        font-size: 36px;
        font-weight: 600; /* SemiBold */
        margin-bottom: 2em;
        line-height: 1.5;
        color: var(--primary-color); /* #ff0000 */
        text-align: center;
    }

    /* FAQ List */
    .faq-list {
        display: flex;
        flex-direction: column;
        gap: 0;
    }

    /* FAQ Item */
    .faq-item {
        border-bottom: 1px solid var(--border-color);
    }

    .faq-item:first-child {
        border-top: 1px solid var(--border-color);
    }

    /* Question Row */
    .faq-question {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5em 0;
        cursor: pointer;
    }

    .faq-question h3 {
        font-family: "Raleway", sans-serif;
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0;
        color: var(--text-color);
    }

    /* Toggle Button */
    .toggle-btn {
        background: none;
        border: none;
        font-size: 2.5rem;
        color: var(--text-color);
        cursor: pointer;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        position: relative;
    }

    .toggle-btn img {
        display: block;
        width: 24px; /* Adjust size as needed */
        height: 24px;
        transition: transform 0.3s ease;
    }

    .toggle-btn .plus {
        display: block;
    }

    .toggle-btn .minus {
        display: none;
    }

    .faq-item.open .toggle-btn .plus {
        display: none;
    }

    .faq-item.open .toggle-btn .minus {
        display: block;
    }

    /* Answer Section */
    .faq-answer {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease, padding 0.3s ease;
    }

    .faq-item.open .faq-answer {
        max-height: 500px; /* Arbitrary large value */
        padding-bottom: 1.5em;
    }

    .faq-answer p {
        font-family: "Raleway", sans-serif;
        font-size: 1rem;
        line-height: 1.6;
        color: var(--secondary-color);
        margin: 0;
    }

    /* Tablet Responsive */
    @media (max-width: 768px) {
        .faq-section {
            padding: calc(var(--spacing-unit) * 2) var(--spacing-unit);
        }

        .faq-container h2 {
            font-size: 30px;
            margin-bottom: 1.5em;
        }

        .faq-question h3 {
            font-size: 1rem;
        }
    }

    /* Mobile Responsive */
    @media (max-width: 480px) {
        .faq-section {
            padding: calc(var(--spacing-unit) * 1.5) var(--spacing-unit);
        }

        .faq-container h2 {
            font-size: 24px;
            margin-bottom: 1.2em;
        }

        .faq-question {
            padding: 1.2em 0;
        }

        .faq-question h3 {
            font-size: 0.95rem;
        }

        .faq-answer p {
            font-size: 0.9rem;
        }
    }
</style>

<script>
    // JavaScript to handle the FAQ toggle functionality
    document.addEventListener('DOMContentLoaded', () => {
        const faqItems = document.querySelectorAll('.faq-item');

        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');

            if (question) {
                question.addEventListener('click', () => {
                    // Toggle the current item
                    item.classList.toggle('open');

                    // Optional: Close other items when one is opened
                    // faqItems.forEach(otherItem => {
                    //     if (otherItem !== item) {
                    //         otherItem.classList.remove('open');
                    //     }
                    // });
                });
            }
        });
    });
</script>
