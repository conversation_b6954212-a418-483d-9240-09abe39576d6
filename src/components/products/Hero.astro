---
// Define the properties the component accepts
interface Props {
    title: string;
    description: string;
    imagePath: string;
    imageAlt: string;
    ctaPrimary: string;
    ctaSecondary: string;
}

// Get the props passed to the component
const { title, description, imagePath, imageAlt, ctaPrimary, ctaSecondary } = Astro.props;
---

<section class="hero-section">
    <div class="hero-container">
        <div class="hero-text-content">
            <h1>{title}</h1>
            <p set:html={description}></p>
            <div class="hero-buttons">
                <a href="#" class="button primary">{ctaPrimary}</a>
                <a href="#" class="button secondary">{ctaSecondary}</a>
            </div>
        </div>
        <div class="hero-image-container">
            <img src={imagePath} alt={imageAlt} loading="lazy" />
        </div>
    </div>
</section>

<style>
    /* Basic variables for easier theme management */
    :root {
        --font-family: 'Raleway', sans-serif;
        --primary-color: #ff0000; /* Example primary color */
        --secondary-color: #6c757d; /* Example secondary color */
        --text-color: #241F20;
        --background-color: #f8f9fa;
        --white-color: #ffffff;
        --border-radius: 4px;
        --spacing-unit: 1rem; /* Base unit for padding/margin */
    }

    /* Style for the main hero section container */
    .hero-section {
        background-color: var(--background-color);
        padding: calc(var(--spacing-unit) * 4) var(--spacing-unit); /* Vertical and horizontal padding */
        width: 100%;
        box-sizing: border-box; /* Include padding in width */
    }

    /* Container for the hero content (text + image) */
    .hero-container {
        max-width: 1140px; /* Limit maximum width */
        margin: 0 auto; /* Center the container */
        display: flex; /* Use flexbox for layout */
        align-items: center; /* Vertically align items in the center */
        gap: calc(var(--spacing-unit) * 3); /* Space between text and image */
        flex-wrap: wrap; /* Allow items to wrap on smaller screens */
    }

    /* Style for the text content block */
    .hero-text-content {
        font-family: var(--font-family);
        flex: 1; /* Allow text block to grow */
        min-width: 300px; /* Minimum width before wrapping */
        color: var(--text-color);
    }

    /* Style for the main heading (h1) */
    .hero-text-content h1 {
        font-family: "Raleway-Bold", sans-serif;
        font-size: 2.5rem; /* Larger font size for heading */
        font-weight: 700;
        margin-bottom: var(--spacing-unit);
        line-height: 1.5;
    }

    /* Style for the description paragraph (p) */
    .hero-text-content p {
        font-family: "Raleway";
        font-size: 1.1rem;
        margin-bottom: calc(var(--spacing-unit) * 1.5);
        line-height: 1.6;
        color: var(--secondary-color); /* Slightly muted color for description */
    }

    /* Ensure strong tags are properly bold */
    .hero-text-content p strong {
        font-family: "Raleway-Bold", sans-serif;
        font-weight: 700;
        color: var(--text-color); /* Make bold text darker for emphasis */
    }

    /* Extra bold class for maximum emphasis */
    .hero-text-content p strong.extra-bold {
        font-family: "Raleway-Black", sans-serif !important;
        font-weight: 900 !important;
        color: var(--text-color) !important;
        display: inline-block !important;
    }

    /* Container for the buttons */
    .hero-buttons {
        display: flex;
        gap: 1em; /* Space between buttons (1em = 16px at default font size) */
        flex-wrap: wrap; /* Allow buttons to wrap if needed */
    }

    /* Base button styles - with higher specificity to override global styles */
    .hero-section .button {
        display: inline-block; /* Allow setting padding/margin */
        padding: 0.75em 1.5em; /* Using em for padding to scale with font size */
        border-radius: 0.25em; /* Using em for border-radius */
        text-decoration: none; /* Remove underline from links */
        font-family: "Raleway", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        font-weight: 500; /* Medium weight */
        font-size: 1.1rem; /* Heading 5 size */
        text-align: center;
        cursor: pointer;
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease; /* Smooth transitions */
        border: 1px solid transparent; /* Base border - updated to 1px */
        font-display: block;
        letter-spacing: 0.01em; /* Slight letter spacing for better readability */
    }

    /* Primary button styles */
    .hero-section .button.primary {
        background-color: var(--primary-color);
        color: var(--white-color);
        border-color: var(--primary-color);
    }

    /* Primary button hover effect */
    .hero-section .button.primary:hover {
        background-color: darken(var(--primary-color), 10%); /* Slightly darker on hover */
        border-color: darken(var(--primary-color), 10%);
    }

    /* Secondary button styles (outline) */
    .hero-section .button.secondary {
        background-color: transparent;
        color: var(--primary-color);
        border-color: var(--primary-color);
        border-width: 1px; /* Reduced from 2px to 1px as requested */
    }

    /* Secondary button hover effect */
    .hero-section .button.secondary:hover {
        background-color: var(--primary-color);
        color: var(--white-color);
    }

    /* Style for the image container */
    .hero-image-container {
        flex: 1; /* Allow image block to grow */
        min-width: 300px; /* Minimum width before wrapping */
        display: flex; /* Center image within container */
        justify-content: center;
        align-items: center;
    }

    /* Style for the image itself */
    .hero-image-container img {
        max-width: 100%; /* Ensure image doesn't overflow */
        height: auto; /* Maintain aspect ratio */

    }

    /* Responsive adjustments for smaller screens */
    @media (max-width: 768px) {
        .hero-container {
            flex-direction: column; /* Stack items vertically */
            text-align: center; /* Center text */
        }

        .hero-text-content h1 {
            font-size: 2rem; /* Slightly smaller heading */
        }

        .hero-text-content p {
            font-size: 1rem; /* Slightly smaller paragraph */
        }

        .hero-buttons {
            justify-content: center; /* Center buttons */
            flex-direction: column; /* Stack buttons vertically */
            width: 100%; /* Full width container */
        }

        .hero-section .button {
            width: 100%; /* Full width buttons */
            margin-bottom: 0.5em; /* Reduced space between stacked buttons (0.5em = 8px at default font size) */
            box-sizing: border-box; /* Include padding in width calculation */
        }

        .hero-section .button:last-child {
            margin-bottom: 0; /* Remove margin from last button */
        }

        .hero-image-container {
            margin-top: calc(var(--spacing-unit) * 2); /* Add space above image when stacked */
        }
    }

    /* iPhone and other mobile devices - exact 16px spacing */
    @media (max-width: 767px) {
        .hero-container {
            gap: 0;
        }
        .hero-buttons {
            gap: 16px; /* Exact 16px gap as specified by designer */
        }

        .hero-section .button {
            margin-bottom: 0; /* Remove margin as we're using gap */
        }
    }

    /* Additional adjustments for very small screens */
    @media (max-width: 480px) {
        .hero-section {
            padding: calc(var(--spacing-unit) * 2) var(--spacing-unit); /* Reduce padding */
        }

        .hero-text-content h1 {
            font-size: 1.8rem; /* Even smaller heading */
        }

        .hero-section .button {
            padding: 0.85em 1.2em; /* Adjust padding using em units */
            font-size: 1rem; /* Maintain readable font size */

        }
        .hero-container {
            gap: 0;
        }
   
    }
</style>
