---
// Define the properties the component accepts
interface Props {
    videoId: string;
    videoTitle: string;
    problemTitle: string;
    introText: string;
    problemList: string[];
    solutionTitle: string;
    solutionText: string;
}

// Get the props passed to the component
const { videoId, videoTitle, problemTitle, introText, problemList, solutionTitle, solutionText } = Astro.props;
---

<section class="problem-solution-section">
    <div class="problem-solution-container">
        <!-- Solution Video -->
        <div class="solution-video">
            <iframe width="560" height="315" src={`https://www.youtube.com/embed/${videoId}`}
                title={videoTitle} style="border: none;" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen>
            </iframe>
        </div>

        <!-- Problem Description -->
        <div class="problem-content">
            <h2>{problemTitle}</h2>
            <p class="intro-text">
                {introText}
            </p>
            <ol class="problem-list">
                {problemList.map((item: string) => (
                    <li>{item}</li>
                ))}
            </ol>
            <h3>{solutionTitle}</h3>
            <p>
                {solutionText}
            </p>
        </div>
    </div>
</section>

<style>
    /* Basic variables for easier theme management */
    :root {
        --font-family: 'Raleway', sans-serif;
        --primary-color: #ff0000; /* Primary color */
        --secondary-color: #6c757d; /* Secondary color */
        --text-color: #241F20; /* Dark text for light background */
        --background-color: #f8f9fa; /* Light background */
        --white-color: #ffffff;
        --border-radius: 4px;
        --spacing-unit: 1rem; /* Base unit for padding/margin */
    }

    /* Problem/Solution Section Styles */
    .problem-solution-section {
        background-color: var(--background-color);
        padding: calc(var(--spacing-unit) * 4) var(--spacing-unit);
        width: 100%;
        box-sizing: border-box;
        color: var(--text-color);
        position: relative;
        z-index: 2;
    }

    .problem-solution-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3em;
        max-width: 1200px;
        margin: 0 auto;
        align-items: center;
    }

    /* Video Container */
    .solution-video {
        width: 100%;
        position: relative;
        overflow: hidden;
        border-radius: var(--border-radius);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .solution-video iframe {
        width: 100%;
        aspect-ratio: 16 / 9;
        border: none;
    }

    /* Problem Content */
    .problem-content {
        font-family: var(--font-family);
        flex: 1;
        min-width: 300px;
        color: var(--text-color);
        display: flex;
        flex-direction: column;
        gap: 1.5em;
    }

    .problem-content h2 {
        font-family: "Raleway", sans-serif;
        font-size: 36px;
        font-weight: 600; /* SemiBold */
        margin-bottom: 0.5em;
        line-height: 54px;
        color: var(--primary-color); /* #ff0000 */
        text-align: left;
    }

    .problem-content h3 {
        font-family: "Raleway", sans-serif;
        font-size: 24px;
        font-weight: 600; /* SemiBold */
        margin-top: 0.5em;
        line-height: 1.5;
        letter-spacing: normal;
        text-align: left;
        color: var(--primary-color); /* #ff0000 */
    }

    .intro-text {
        font-family: "Raleway";
        font-size: 1.2rem;
        line-height: 1.6;
        color: var(--secondary-color);
        margin-bottom: 1em;
    }

    .problem-list {
        list-style: none;
        counter-reset: problem-counter;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: 1em;
    }

    .problem-list li {
        position: relative;
        padding-left: 3em;
        font-size: 1.1rem;
        line-height: 1.5;
        color: var(--secondary-color);
        counter-increment: problem-counter;
    }

    .problem-list li::before {
        content: counter(problem-counter);
        position: absolute;
        left: 0;
        top: 0.1em;
        width: 1.8em;
        height: 1.8em;
        background-color: var(--primary-color);
        border-radius: 50%;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.9em;
    }

    .problem-content p {
        font-family: "Raleway";
        font-size: 1.1rem;
        line-height: 1.6;
        color: var(--secondary-color);
    }

    /* Tablet Responsive */
    @media (max-width: 992px) {
        .problem-solution-container {
            gap: 2em;
        }

        .problem-content h2 {
            font-size: 1.8rem;
        }

        .problem-content h3 {
            font-size: 22px;
        }

        .intro-text {
            font-size: 1.1rem;
        }

        .problem-list li {
            font-size: 1rem;
        }
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .problem-solution-section {
            padding: calc(var(--spacing-unit) * 2) var(--spacing-unit);
        }

        .problem-solution-container {
            grid-template-columns: 1fr;
            gap: 2.5em;
        }

        .solution-video {
            order: 2; /* Move video below content on mobile */
        }

        .problem-content {
            order: 1; /* Move content above video on mobile */
            text-align: center;
        }

        .problem-content h2 {
            font-size: 30px;
            line-height: 45px;
            text-align: center;
        }

        .problem-content p {
            font-size: 1rem;
        }

        .problem-content h3 {
            text-align: center;
        }

        .problem-list {
            align-items: flex-start;
            margin-left: 1em;
        }

        .problem-list li {
            text-align: left;
        }
    }

    /* Small Mobile Adjustments */
    @media (max-width: 480px) {
        .problem-solution-section {
            padding: calc(var(--spacing-unit) * 1.5) var(--spacing-unit);
        }

        .problem-solution-container {
            gap: 2em;
        }

        .problem-content h2 {
            font-size: 24px;
            line-height: 36px;
        }

        .problem-content h3 {
            font-size: 20px;
        }

        .intro-text {
            font-size: 1rem;
        }

        .problem-list {
            gap: 0.8em;
        }

        .problem-list li {
            font-size: 0.95rem;
            padding-left: 2.5em;
        }

        .problem-list li::before {
            width: 1.6em;
            height: 1.6em;
            top: 0.1em;
        }
    }
</style>
