---
// Define the properties the component accepts
interface Testimonial {
    quote: string;
    name: string;
    title: string;
    company: string;
    imageSrc: string;
}

interface Props {
    heading: string;
    testimonials: Testimonial[];
}

// Get the props passed to the component
const { heading, testimonials } = Astro.props;
---

<section class="testimonials-section">
    <div class="testimonials-container">
        <h2>{heading}</h2>
        <div class="testimonials-flex">
            {testimonials.map((testimonial) => (
                <div class="testimonial-card">
                    <p class="quote">"{testimonial.quote}"</p>
                    <div class="profile">
                        <div class="profile-image">
                            <img src="/images/products/testimony_customer.png" alt={`${testimonial.name} profile photo`} />
                        </div>
                        <div class="profile-info">
                            <h3>{testimonial.name}</h3>
                            <p class="title">{testimonial.title}</p>
                            <p class="company">{testimonial.company}</p>
                        </div>
                    </div>
                </div>
            ))}
        </div>
    </div>
</section>

<style>
    /* Basic variables for easier theme management */
    :root {
        --font-family: 'Raleway', sans-serif;
        --primary-color: #ff0000; /* Primary color */
        --secondary-color: #6c757d; /* Secondary color */
        --text-color: #241F20; /* Dark text */
        --background-color: #f8f8f8; /* Light background */
        --white-color: #ffffff;
        --border-radius: 4px;
        --spacing-unit: 1rem; /* Base unit for padding/margin */
    }

    /* Testimonials Section Styles */
    .testimonials-section {
        background-color: #f5f5f5; /* Light gray background for the entire section */
        padding: calc(var(--spacing-unit) * 4) var(--spacing-unit);
        width: 100%;
        box-sizing: border-box;
        position: relative;
        z-index: 2;
    }

    .testimonials-container {

        max-width: 1444px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    /* Heading Styles */
    .testimonials-container h2 {
        margin-top: -0.2em;
        font-family: "Raleway", sans-serif;
        font-size: 36px;
        font-weight: 600; /* SemiBold */
        margin-bottom: 2em;
        line-height: 1.5;
        color: #ff0000; /* Red color for the heading */
        text-align: center;
    }

    /* Testimonials Flex Container */
    .testimonials-flex {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 2em;
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        align-items: stretch; /* Ensures all items stretch to match the tallest item */
        padding: 1em 0 3em; /* Add some padding at the bottom */
    }

    /* Testimonial Card */
    .testimonial-card {
        background-color: #ffffff; /* Pure white background */
        border-radius: 4px;
        padding: 2em;
        /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); */
        display: flex;
        flex-direction: column;
        height: 100%; /* Takes full height of the flex container */
        flex: 0 1 calc(33.333% - 2em);
        min-width: 280px;
        max-width: 448px; 
    }

    /* Quote Text */
    .testimonial-card .quote {
        background-color: var(--white-color);
        font-family: "Raleway", sans-serif;
        font-size: 1rem;
        line-height: 1.6;
        color: var(--text-color);
        margin-bottom: 2em;
        flex-grow: 1; /* This makes the quote section expand to fill available space */
        display: flex;
        flex-direction: column;
        padding: 0;
        text-align: center;
        /* font-style: italic; */
    }

    /* Profile Section */
    .profile {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        margin-top: auto; /* This pushes the profile section to the bottom of the card */
        padding-top: 1em;
    }

    /* Profile Image */
    .profile-image {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        overflow: hidden;
        margin-bottom: 1em;
    }

    .profile-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* Profile Info */
    .profile-info h3 {
        font-family: "Raleway", sans-serif;
        font-size: 1.2rem;
        font-weight: 600; /* SemiBold */
        margin-bottom: 0.3em;
        color: var(--text-color);
    }

    .profile-info .title {
        font-family: "Raleway", sans-serif;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.2em;
        color: #ff0000; /* Red color for title */
    }

    .profile-info .company {
        font-family: "Raleway", sans-serif;
        font-size: 0.9rem;
        color: var(--secondary-color);
        margin: 0;
    }

    /* Tablet Responsive */
    @media (max-width: 992px) {
        .testimonials-flex {
            gap: 1.5em;
        }

        .testimonial-card {
            padding: 1.5em;
            min-width: 240px;
            max-width: 350px;
            flex: 0 1 calc(50% - 1.5em);
        }

        .testimonial-card .quote {
            font-size: 0.95rem;
        }

        .profile-image {
            width: 70px;
            height: 70px;
        }

        .profile-info h3 {
            font-size: 1.1rem;
        }
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .testimonials-section {
            padding: calc(var(--spacing-unit) * 2) var(--spacing-unit);
        }

        .testimonials-container h2 {
            font-size: 30px;
            margin-bottom: 1.5em;
        }

        .testimonials-flex {
            flex-direction: column;
            align-items: center;
            gap: 1.5em;
        }

        .testimonial-card {
            min-width: 100%;
            max-width: 500px;
            flex: 0 1 100%;
        }
    }

    /* Small Mobile Adjustments */
    @media (max-width: 480px) {
        .testimonials-section {
            padding: calc(var(--spacing-unit) * 1.5) var(--spacing-unit);
        }

        .testimonials-container h2 {
            font-size: 24px;
        }

        .testimonial-card {
            padding: 1.2em;
        }

        .testimonial-card .quote {
            font-size: 0.9rem;
            margin-bottom: 1.5em;
        }

        .profile-image {
            width: 60px;
            height: 60px;
        }

        .profile-info h3 {
            font-size: 1rem;
        }

        .profile-info .title {
            font-size: 0.9rem;
        }

        .profile-info .company {
            font-size: 0.8rem;
        }
    }
</style>
