---
// Define the properties the component accepts
interface Props {
    heading: string;
    subheading: string;
    primaryButtonText: string;
    secondaryButtonText: string;
    primaryButtonUrl?: string;
    secondaryButtonUrl?: string;
}

// Get the props passed to the component
const {
    heading,
    subheading,
    primaryButtonText,
    secondaryButtonText,
    primaryButtonUrl = "#",
    secondaryButtonUrl = "#"
} = Astro.props;
---

<section class="cta-strip">
    <div class="cta-container">
        <div class="cta-content">
            <h2>{heading}</h2>
            <p>{subheading}</p>
            <div class="cta-buttons">
                <a href={primaryButtonUrl} class="button primary">{primaryButtonText}</a>
                <a href={secondaryButtonUrl} class="button secondary">{secondaryButtonText}</a>
            </div>
        </div>
    </div>
</section>

<style>
    /* Basic variables for easier theme management */
    :root {
        --font-family: 'Raleway', sans-serif;
        --primary-color: #ff0000; /* Primary color */
        --secondary-color: #6c757d; /* Secondary color */
        --text-color: #241F20; /* Dark text */
        --background-color: #f8f9fa; /* Light background */
        --white-color: #ffffff;
        --border-radius: 4px;
        --spacing-unit: 1rem; /* Base unit for padding/margin */
    }

    /* CTA Strip Styles */
    .cta-strip {
        background-color: var(--white-color);
        padding: calc(var(--spacing-unit) * 3) var(--spacing-unit);
        width: 100%;
        box-sizing: border-box;
    }

    .cta-container {
        max-width: 800px;
        margin: 0 auto;
        display: flex;
        justify-content: center;
    }

    .cta-content {
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    /* Heading Styles */
    .cta-content h2 {
        font-family: "Raleway", sans-serif;
        font-size: 2rem;
        font-weight: 600; /* SemiBold */
        margin-bottom: 0.5em;
        line-height: 1.3;
        color: var(--text-color);
    }

    /* Subheading Styles */
    .cta-content p {
        font-family: "Raleway", sans-serif;
        font-size: 1.1rem;
        line-height: 1.5;
        margin-bottom: 2em;
        color: var(--secondary-color);
    }

    /* Buttons Container */
    .cta-buttons {
        display: flex;
        gap: 1em;
        justify-content: center;
    }

    /* Button Styles */
    .button {
        display: inline-block;
        padding: 0.75em 1.5em;
        border-radius: var(--border-radius);
        text-decoration: none;
        font-family: "Raleway", sans-serif;
        font-weight: 500;
        font-size: 1rem;
        text-align: center;
        cursor: pointer;
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
        border: 1px solid transparent;
    }

    /* Primary Button */
    .button.primary {
        background-color: var(--primary-color);
        color: var(--white-color);
        border-color: var(--primary-color);
    }

    .button.primary:hover {
        background-color: #e60000; /* Slightly darker red on hover */
        border-color: #e60000;
    }

    /* Secondary Button */
    .button.secondary {
        background-color: transparent;
        color: var(--primary-color);
        border-color: var(--primary-color);
        border-width: 1px;
    }

    .button.secondary:hover {
        background-color: rgba(255, 0, 0, 0.05); /* Very light red background on hover */
    }

    /* Tablet Responsive */
    @media (max-width: 768px) {
        .cta-strip {
            padding: calc(var(--spacing-unit) * 2) var(--spacing-unit);
        }

        .cta-content h2 {
            font-size: 1.8rem;
        }

        .cta-content p {
            font-size: 1rem;
            margin-bottom: 1.5em;
        }
    }

    /* Mobile Responsive */
    @media (max-width: 480px) {
        .cta-strip {
            padding: calc(var(--spacing-unit) * 1.5) var(--spacing-unit);
        }

        .cta-content h2 {
            font-size: 1.5rem;
        }

        .cta-content p {
            font-size: 0.95rem;
            margin-bottom: 1.2em;
        }

        .cta-buttons {
            flex-direction: column;
            width: 100%;
            gap: 0.5em;
        }

        .button {
            width: 100%;
            box-sizing: border-box;
        }
    }
</style>
