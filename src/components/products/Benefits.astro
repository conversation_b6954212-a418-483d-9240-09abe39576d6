---
// Define the properties the component accepts
interface BenefitCard {
    title: string;
    description: string;
    iconSrc: string;
    iconAlt?: string;
}

interface Props {
    heading: string;
    benefits: BenefitCard[];
}

// Get the props passed to the component
const { heading, benefits } = Astro.props;
---

<section class="benefits-section">
    <div class="benefits-container">
        <h2>{heading}</h2>
        <div class="benefits-grid">
            {benefits.map((benefit) => (
                <div class="benefit-card">
                    <div class="icon-container">
                        <img src={benefit.iconSrc} alt={benefit.iconAlt || `${benefit.title} icon`} />
                    </div>
                    <h3>{benefit.title}</h3>
                    <p>{benefit.description}</p>
                </div>
            ))}
        </div>
    </div>
</section>

<style>
    /* Basic variables for easier theme management */
    :root {
        --font-family: 'Raleway', sans-serif;
        --primary-color: #ff0000; /* Primary color */
        --secondary-color: #6c757d; /* Secondary color */
        --text-color: #241F20; /* Dark text */
        --background-color: #f8f9fa; /* Light background */
        --white-color: #ffffff;
        --border-radius: 4px;
        --spacing-unit: 1rem; /* Base unit for padding/margin */
    }

    /* Benefits Section Styles */
    .benefits-section {
        background-color: var(--white-color);
        padding: calc(var(--spacing-unit) * 4) var(--spacing-unit);
        width: 100%;
        box-sizing: border-box;
        position: relative;
        z-index: 2;
    }

    .benefits-container {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    /* Heading Styles */
    .benefits-container h2 {
        font-family: "Raleway", sans-serif;
        font-size: 36px;
        font-weight: 600; /* SemiBold */
        margin-bottom: 2em;
        line-height: 1.5;
        color: var(--primary-color); /* #ff0000 */
        text-align: center;
    }

    /* Benefits Grid */
    .benefits-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2em;
        width: 100%;
    }

    /* Benefit Card */
    .benefit-card {
        background-color: var(--white-color);
        padding: 2em;
        display: flex;
        flex-direction: column;
    }

    /* Icon Container */
    .icon-container {
        width: 48px;
        height: 48px;
        margin-bottom: 1em;
        display: flex;
        align-items: center;
        justify-content: flex-start; /* Left aligned */
    }

    .icon-container img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        display: block; /* Ensures proper rendering */
    }

    /* Specific SVG handling */
    .icon-container svg {
        width: 100%;
        height: 100%;
        display: block;
    }

    /* Card Title */
    .benefit-card h3 {
        font-family: "Raleway", sans-serif;
        font-size: 1.5rem;
        font-weight: 600; /* SemiBold */
        margin-bottom: 0.8em;
        color: #000; /* Black color as specified */
        text-align: left;
    }

    /* Card Description */
    .benefit-card p {
        font-family: "Raleway", sans-serif;
        font-size: 1.1rem;
        line-height: 1.6;
        color: var(--secondary-color);
    }

    /* Tablet Responsive */
    @media (max-width: 992px) {
        .benefits-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5em;
        }

        .benefit-card {
            padding: 1.5em;
        }

        .icon-container {
            width: 40px;
            height: 40px;
            margin-bottom: 0.8em;
        }

        .benefit-card h3 {
            font-size: 1.3rem;
        }

        .benefit-card p {
            font-size: 1rem;
        }
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .benefits-section {
            padding: calc(var(--spacing-unit) * 2) var(--spacing-unit);
        }

        .benefits-container h2 {
            font-size: 30px;
            margin-bottom: 1.5em;
        }

        .benefits-grid {
            grid-template-columns: 1fr;
            gap: 1.5em;
        }
    }

    /* Small Mobile Adjustments */
    @media (max-width: 480px) {
        .benefits-section {
            padding: calc(var(--spacing-unit) * 1.5) var(--spacing-unit);
        }

        .benefits-container h2 {
            font-size: 24px;
        }

        .benefit-card {
            padding: 1.2em;
        }

        .icon-container {
            width: 36px;
            height: 36px;
            margin-bottom: 0.7em;
        }

        .benefit-card h3 {
            font-size: 1.2rem;
        }

        .benefit-card p {
            font-size: 0.95rem;
        }
    }
</style>
