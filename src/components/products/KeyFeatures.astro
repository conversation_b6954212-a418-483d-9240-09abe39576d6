---
// Define the properties the component accepts
interface Feature {
    title: string;
    description: string;
}

interface Props {
    heading: string;
    features: Feature[];
}

// Get the props passed to the component
const { heading, features } = Astro.props;
---

<section class="key-features-section">
    <div class="key-features-container">
        <h2>{heading}</h2>
        <div class="features-grid">
            {features.map((feature) => (
                <div class="feature-card">
                    <h3>{feature.title}</h3>
                    <p>{feature.description}</p>
                </div>
            ))}
        </div>
    </div>
</section>

<style>
    /* Basic variables for easier theme management */
    :root {
        --font-family: 'Raleway', sans-serif;
        --primary-color: #ff0000; /* Primary color */
        --secondary-color: #6c757d; /* Secondary color */
        --text-color: #241F20; /* Dark text */
        --background-color: #f8f9fa; /* Light background */
        --white-color: #ffffff;
        --border-radius: 4px;
        --spacing-unit: 1rem; /* Base unit for padding/margin */
    }
    
    /* Key Features Section Styles */
    .key-features-section {
        background-color: var(--white-color);
        padding: calc(var(--spacing-unit) * 4) var(--spacing-unit);
        width: 100%;
        box-sizing: border-box;
        border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
        position: relative;
        z-index: 2;
    }
    
    .key-features-container {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    /* Heading Styles */
    .key-features-container h2 {
        font-family: "Raleway", sans-serif;
        font-size: 36px;
        font-weight: 600; /* SemiBold */
        margin-bottom: 3em;
        line-height: 1.5;
        color: var(--primary-color); /* #ff0000 */
        text-align: center;
    }
    
    /* Features Grid */
    .features-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2em;
        width: 100%;
        margin-bottom: 2em;
    }
    
    /* Second row with different styling */
    .features-grid > div:nth-child(n+4) {
        margin-top: 2em;
    }
    
    /* Feature Card */
    .feature-card {
        border: solid 1px #f8f8f8;
        background-color: rgba(248, 248, 248, 0.3);
        -webkit-backdrop-filter: blur(50px);
        backdrop-filter: blur(50px);
        padding: 1.5em;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .feature-card:hover {
        background-color: #F8F8F8;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
    }
    
    /* Feature Title */
    .feature-card h3 {
        font-family: "Raleway", sans-serif;
        font-size: 1.3rem;
        font-weight: 600; /* SemiBold */
        margin-bottom: 1em;
        color: var(--text-color);
        text-align: center;
        line-height: 1.4;
    }
    
    /* Feature Description */
    .feature-card p {
        font-family: "Raleway", sans-serif;
        font-size: 1rem;
        line-height: 1.6;
        color: var(--secondary-color);
        text-align: center;
    }
    
    /* Tablet Responsive */
    @media (max-width: 992px) {
        .features-grid {
            gap: 1.5em;
        }
        
        .feature-card h3 {
            font-size: 1.2rem;
        }
        
        .feature-card p {
            font-size: 0.95rem;
        }
    }
    
    /* Mobile Responsive */
    @media (max-width: 768px) {
        .key-features-section {
            padding: calc(var(--spacing-unit) * 2) var(--spacing-unit);
        }
        
        .key-features-container h2 {
            font-size: 30px;
            margin-bottom: 2em;
        }
        
        .features-grid {
            grid-template-columns: 1fr;
            gap: 1.5em;
        }
        
        .features-grid > div:nth-child(n+4) {
            margin-top: 0;
        }
    }
    
    /* Small Mobile Adjustments */
    @media (max-width: 480px) {
        .key-features-section {
            padding: calc(var(--spacing-unit) * 1.5) var(--spacing-unit);
        }
        
        .key-features-container h2 {
            font-size: 24px;
            margin-bottom: 1.5em;
        }
        
        .feature-card {
            padding: 1.2em;
        }
        
        .feature-card h3 {
            font-size: 1.1rem;
            margin-bottom: 0.8em;
        }
        
        .feature-card p {
            font-size: 0.9rem;
        }
    }
</style>
