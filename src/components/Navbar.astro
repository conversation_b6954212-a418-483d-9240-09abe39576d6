---
import PageLink from "./navbar/PageLink.astro";
import { useTranslations } from "../utils/i18n";

const { locale = 'en' } = Astro.props;

// Debug: Check if we're on a French URL and force French locale
import { getLangFromUrl } from "../utils/i18n";
const urlLocale = getLangFromUrl(Astro.url);
const debugLocale = Astro.url.pathname.includes('/fr') ? 'fr' : locale;

const { t } = useTranslations(debugLocale);

//Section categories
const txnBknHdr = t("nav.transaction_banking", "Transaction Banking Solutions");
const chnBknHdr = t("nav.channel_banking", "Channel Banking Solutions");
const dgtTsnHdr = t("nav.digital_transformation", "Digital Transformation Solutions");

const ProductCategory = {
    TRANSACTION_BANKING: "TRANSACTION_BANKING",
    CHANNEL_BANKING: "CHANNEL_BANKING",
    DIGITAL_TRANSFORMATION: "DIGITAL_TRANSFORMATION"
};

//🔥NOTE: Content Management Guideline
// To determing whether a link renders or not, toggle the pageActive property between true or false
const productLinks = [
    {
        pageActive: true,
        title: t("products.omnicollect.title", "OmniCollect"),
        description: t("products.omnicollect.description", "OmniCollect Description"),
        pageLocation: "/products/omnicollect",
        category: ProductCategory.TRANSACTION_BANKING,
    },
    {
        pageActive: true,
        title: t("products.gp_ddacc.title", "GP DDACC"),
        description: t("products.gp_ddacc.description", "Premier No-code USSD platform for bank channel strategy"),
        pageLocation: "/products/ussdhub",
        category: ProductCategory.TRANSACTION_BANKING,
    },
    {
        pageActive: true,
        title: t("products.gp_cash.title", "GP CASH"),
        description: t("products.gp_cash.description", "Premier No-code USSD platform for bank channel strategy"),
        pageLocation: "/products/ussdhub",
        category: ProductCategory.TRANSACTION_BANKING,
    },
    {
        pageActive: true,
        title: t("products.gp_scf.title", "GP SCF"),
        description: t("products.gp_scf.description", "Premier No-code USSD platform for bank channel strategy"),
        pageLocation: "/products/ussdhub",
        category: ProductCategory.TRANSACTION_BANKING,
    },
    {
        pageActive: true,
        title: t("products.ussdhub.title", "USSDHub"),
        description: t("products.ussdhub.description", "The world's most advanced No-Code USSD application development platform"),
        pageLocation: "/products/ussdhub",
        category: ProductCategory.CHANNEL_BANKING,
    },
    {
        pageActive: true,
        title: t("products.gp_agency_banking.title", "GP Agency Banking"),
        description: t("products.gp_agency_banking.description", "Premier No-code USSD platform for bank channel strategy"),
        pageLocation: "/products/agency-banking",
        category: ProductCategory.CHANNEL_BANKING,
    },
    {
        pageActive: true,
        title: t("products.gp_digital_banking.title", "GP Digital Banking"),
        description: t("products.gp_digital_banking.description", "Premier No-code USSD platform for bank channel strategy"),
        pageLocation: "/products/digital-banking",
        category: ProductCategory.CHANNEL_BANKING,
    },
    {
        pageActive: true,
        title: t("products.api_gateway.title", "API Gateway & Developer Portal Implementations"),
        description: t("products.api_gateway.description", "We implement end-to-end API gateways using IBM, Kong, and other solutions to accelerate banks' digital transformation"),
        pageLocation: "/products/api-gateway",
        category: ProductCategory.DIGITAL_TRANSFORMATION,
    },
    {
        pageActive: true,
        title: t("products.bank_team_resourcing.title", "Bank Team Resourcing"),
        description: t("products.bank_team_resourcing.description", "We provide skilled technology professionals with deep banking expertise to support your existing technology projects"),
        pageLocation: "/services/bank-team-resourcing",
        category: ProductCategory.DIGITAL_TRANSFORMATION,
    },
    {
        pageActive: true,
        title: t("products.custom_banking_software.title", "Custom Banking Software Development"),
        description: t("products.custom_banking_software.description", "We deliver innovative, secure, and scalable solutions for modern financial institutions"),
        pageLocation: "/services/custom-banking-software",
        category: ProductCategory.DIGITAL_TRANSFORMATION,
    },
];
---

<nav class="navbar">
    <ul>
        <li>
            <a href={debugLocale === 'en' ? '/' : `/${debugLocale}`} class="logo-container"
                ><img
                    src="/images/logo.png"
                    class="logo"
                    alt="Grey Parrot Logo"
                /></a
            >
        </li>
        <li>
            <a
                href="javascript:void(0);"
                class="dropdown-trigger nav-desktop-link"
            >
                <span>{t("nav.products", "Products & Services")}</span>
                <img
                    src="/images/arrow-down.png"
                    class="nav-icon"
                    alt="Grey Parrot Navbar Dropdown Image"
                />
            </a>
            <div
                style="display: flex; justify-content: center;"
                class="dropdown-content desktop"
            >
                <section>
                    <h6>{txnBknHdr}</h6>
                    {
                        productLinks
                            .filter(
                                (link) =>
                                    link.pageActive &&
                                    link.category == "TRANSACTION_BANKING",
                            )
                            .map((link) => (
                                <PageLink
                                    title={link.title}
                                    description={link.description}
                                    pageLocation={link.pageLocation}
                                    locale={debugLocale}
                                />
                            ))
                    }
                </section>
                <section>
                    <h6>{chnBknHdr}</h6>
                    {
                        productLinks
                            .filter(
                                (link) =>
                                    link.pageActive &&
                                    link.category == "CHANNEL_BANKING",
                            )
                            .map((link) => (
                                <PageLink
                                    title={link.title}
                                    description={link.description}
                                    pageLocation={link.pageLocation}
                                    locale={debugLocale}
                                />
                            ))
                    }
                </section>
                <section>
                    <h6>{dgtTsnHdr}</h6>
                    {
                        productLinks
                            .filter(
                                (link) =>
                                    link.pageActive &&
                                    link.category == "DIGITAL_TRANSFORMATION",
                            )
                            .map((link) => (
                                <PageLink
                                    title={link.title}
                                    description={link.description}
                                    pageLocation={link.pageLocation}
                                    locale={debugLocale}
                                />
                            ))
                    }
                </section>
            </div>
        </li>

        <li>
            <a
                href="javascript:void(0);"
                class="dropdown-trigger nav-desktop-link"
            >
                <span>{t("nav.company", "Company")}</span>
                <img
                    src="/images/arrow-down.png"
                    class="nav-icon"
                    alt="Grey Parrot Navbar Dropdown Image"
                />
            </a>
            <div class="dropdown-content desktop">
                <section>
                    <h6>{t("nav.company_header", "COMPANY")}</h6>
                    <div class="dropdown-link">
                        <a href={debugLocale === 'en' ? '/company' : `/${debugLocale}/company`} class="normal-weight">{t("nav.our_story", "Our Story")}</a>
                        <p></p>
                    </div>
                    <div class="dropdown-link">
                        <a href={debugLocale === 'en' ? '/company' : `/${debugLocale}/company`} class="normal-weight"
                            >{t("nav.guiding_values", "Guiding Values")}</a
                        >
                        <p></p>
                    </div>
                    <div class="dropdown-link">
                        <a href={debugLocale === 'en' ? '/company' : `/${debugLocale}/company`} class="normal-weight"
                            >{t("nav.vision_strategy", "Vision & Strategy")}</a
                        >
                        <p></p>
                    </div>
                </section>
                <section>
                    <h6>{t("nav.resources", "RESOURCES")}</h6>
                    <div class="dropdown-link">
                        <a href={debugLocale === 'en' ? '/case-studies' : `/${debugLocale}/case-studies`} class="normal-weight"
                            >{t("nav.case_studies", "Case Studies")}</a
                        >
                        <p></p>
                    </div>
                </section>
            </div>
        </li>
        <li><a href={debugLocale === 'en' ? '/#partners' : `/${debugLocale}#partners`}>{t("nav.partners", "Partners")}</a></li>
        <li>
            <button
                onclick={`window.location.href = '${debugLocale === 'en' ? '/contact-us' : `/${debugLocale}/contact-us`}'`}
                class="contact-us">{t("nav.contact", "Contact Us")}</button
            >
        </li>
        <li>
            <a href="tel:+233206612483" class="flex"
                ><img
                    src="/images/call.png"
                    width="20"
                    loading="lazy"
                    alt="Grey Parrot Contact Us"
                /><span class="ff-montserrat-sb text-danger call-number"
                    >+233 20 661 2483</span
                ></a
            >
        </li>
        <!-- Mobile Navbar Menu -->
        <li>
            <a
                href="javascript:void(0);"
                class="dropdown-trigger"
                id="mobile-trigger"
                ><img
                    src="/images/menu.png"
                    class="menu"
                    alt="Grey Parrot Menu"
                /></a
            >
            <div
                class="dropdown-content mobile"
                id="mobile-content"
                tabindex="0"
            >
                <ul>
                    <li>
                        <a href="javascript:void(0);" class="nav-mobile-link">
                            <span>{t("nav.products", "Products & Services")}</span>
                            <img
                                src="/images/arrow-down.png"
                                alt="Grey Parrot Products"
                            />
                        </a>
                        <div class="nav-mobile-dropdown-content" tabindex="0">
                            <a
                                href="javascript:void(0);"
                                class="nav-mobile-return-link"
                            >
                                <img
                                    src="/images/arrow-down.png"
                                    alt="Grey Parrot Products"
                                />
                                <span>{t("nav.mobile_return_products", "Products")}</span>
                            </a>
                            <section>
                                <h6>{txnBknHdr}</h6>
                                {
                                    productLinks
                                        .filter((link) => link.pageActive && link.category == "TRANSACTION_BANKING")
                                        .map((link) => (
                                            <PageLink
                                                title={link.title}
                                                description={link.description}
                                                pageLocation={link.pageLocation}
                                                locale={debugLocale}
                                            />
                                        ))
                                }

                            </section>
                            <section>
                                <h6>{chnBknHdr}</h6>
                                {
                                    productLinks
                                        .filter(
                                            (link) =>
                                                link.pageActive &&
                                                link.category ===
                                                    "CHANNEL_BANKING",
                                        )
                                        .map((link) => (
                                            <PageLink
                                                title={link.title}
                                                description={link.description}
                                                pageLocation={link.pageLocation}
                                                locale={debugLocale}
                                            />
                                        ))
                                }
                            </section>
                            <section>
                                <h6>{dgtTsnHdr}</h6>
                                {
                                    productLinks
                                        .filter(
                                            (link) =>
                                                link.pageActive &&
                                                link.category ===
                                                    "DIGITAL_TRANSFORMATION",
                                        )
                                        .map((link) => (
                                            <PageLink
                                                title={link.title}
                                                description={link.description}
                                                pageLocation={link.pageLocation}
                                                locale={debugLocale}
                                            />
                                        ))
                                }
                            </section>
                        </div>
                    </li>

                    <li>
                        <a href="javascript:void(0);" class="nav-mobile-link">
                            <span>{t("nav.company", "Company")}</span>
                            <img
                                src="/images/arrow-down.png"
                                alt="Grey Parrot Company"
                            />
                        </a>
                        <div class="nav-mobile-dropdown-content" tabindex="0">
                            <a
                                href="javascript:void(0);"
                                class="nav-mobile-return-link"
                            >
                                <img
                                    src="/images/arrow-down.png"
                                    alt="Grey Parrot Company"
                                />
                                <span>{t("nav.mobile_return_company", "Company")}</span>
                            </a>
                            <section>
                                <h6>{t("nav.company_header", "COMPANY")}</h6>
                                <div class="dropdown-link">
                                    <a href={debugLocale === 'en' ? '/company' : `/${debugLocale}/company`} class="normal-weight"
                                        >{t("nav.our_story", "Our Story")}</a
                                    >
                                    <p></p>
                                </div>
                                <div class="dropdown-link">
                                    <a href={debugLocale === 'en' ? '/company' : `/${debugLocale}/company`} class="normal-weight"
                                        >{t("nav.guiding_values", "Guiding Values")}</a
                                    >
                                    <p></p>
                                </div>
                                <div class="dropdown-link">
                                    <a href={debugLocale === 'en' ? '/company' : `/${debugLocale}/company`} class="normal-weight"
                                        >{t("nav.vision_strategy", "Vision & Strategy")}</a
                                    >
                                    <p></p>
                                </div>
                            </section>
                            <section>
                                <h6>{t("nav.resources", "RESOURCES")}</h6>
                                <div class="dropdown-link">
                                    <a
                                        href={debugLocale === 'en' ? '/case-studies' : `/${debugLocale}/case-studies`}
                                        class="normal-weight">{t("nav.case_studies", "Case Studies")}</a
                                    >
                                    <p></p>
                                </div>
                            </section>
                        </div>
                    </li>
                    <li><a href={debugLocale === 'en' ? '/#partners' : `/${debugLocale}#partners`}>{t("nav.partners", "Partners")}</a></li>
                    <li>
                        <button
                            class="contact-us"
                            onclick={`window.location.href = '${debugLocale === 'en' ? '/contact-us' : `/${debugLocale}/contact-us`}'`}
                            >{t("nav.contact", "Contact Us")}</button
                        >
                    </li>
                    <li>
                        <a href="tel:+233206612483" class="flex flex-start"
                            ><img
                                src="/images/call.png"
                                width="20"
                                loading="lazy"
                                alt="Grey Parrot Contact Us"
                            /><span
                                class="ff-montserrat-sb text-danger call-number"
                                >+233 20 661 2483</span
                            ></a
                        >
                    </li>
                </ul>
            </div>
        </li>
    </ul>
</nav>

<style>
    h6 {
        color: red;
    }
    /* Ensure sections are equal width for non-mobile displays */
    @media screen and (min-width: 768px) {
        section {
            flex: 1;
        }
    }
</style>
