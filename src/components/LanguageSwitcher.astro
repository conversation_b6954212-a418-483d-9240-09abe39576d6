---
import { getLangFromUrl } from "../utils/i18n";

const currentLocale = getLangFromUrl(Astro.url);

// Get the current path without the locale prefix
const currentPath = Astro.url.pathname.replace(/^\/(en|fr)/, '') || '/';

// Generate URLs for language switching
const englishUrl = currentPath === '/' ? '/en' : `/en${currentPath}`;
const frenchUrl = currentPath === '/' ? '/fr' : `/fr${currentPath}`;
---

<div class="language-switcher">
    <a 
        href={englishUrl} 
        class={`lang-option ${currentLocale === 'en' ? 'active' : ''}`}
        aria-label="Switch to English"
    >
        EN
    </a>
    <span class="separator">|</span>
    <a 
        href={frenchUrl} 
        class={`lang-option ${currentLocale === 'fr' ? 'active' : ''}`}
        aria-label="Switch to French"
    >
        FR
    </a>
</div>

<style>
    .language-switcher {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-family: "Raleway", sans-serif;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .lang-option {
        color: #666;
        text-decoration: none;
        padding: 0.25rem 0.5rem;
        border-radius: 3px;
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .lang-option:hover {
        color: #ff0000;
        background-color: rgba(255, 0, 0, 0.05);
    }

    .lang-option.active {
        color: #ff0000;
        font-weight: 600;
        background-color: rgba(255, 0, 0, 0.1);
    }

    .separator {
        color: #ccc;
        font-weight: 300;
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
        .language-switcher {
            font-size: 0.85rem;
            gap: 0.4rem;
        }
        
        .lang-option {
            padding: 0.2rem 0.4rem;
        }
    }
</style>
