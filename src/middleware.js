// src/middleware.js
function parseAcceptLanguage(acceptLanguage) {
  // Default to English
  if (!acceptLanguage) return 'en';
  
  // Parse the Accept-Language header
  const languages = acceptLanguage.split(',').map(lang => {
    const [code, q = '1'] = lang.trim().split(';q=');
    return { code: code.split('-')[0], q: parseFloat(q) };
  }).sort((a, b) => b.q - a.q);
  
  // Check for supported languages (English, French)
  const supportedLangs = ['en', 'fr'];
  for (const lang of languages) {
    if (supportedLangs.includes(lang.code)) {
      return lang.code;
    }
  }
  
  // Default to English if no supported language found
  return 'en';
}

export const onRequest = async ({ request, redirect }, next) => {
  const url = new URL(request.url);
  
  // Skip middleware for static assets and API routes
  if (url.pathname.startsWith('/_astro/') || 
      url.pathname.startsWith('/api/') ||
      url.pathname.startsWith('/images/') ||
      url.pathname.startsWith('/js/') ||
      url.pathname.endsWith('.css') ||
      url.pathname.endsWith('.js') ||
      url.pathname.endsWith('.json') ||
      url.pathname.endsWith('.png') ||
      url.pathname.endsWith('.jpg') ||
      url.pathname.endsWith('.jpeg') ||
      url.pathname.endsWith('.gif') ||
      url.pathname.endsWith('.svg') ||
      url.pathname.endsWith('.ico') ||
      url.pathname.endsWith('.webp')) {
    return next();
  }
  
  // Handle root path
  if (url.pathname === '/' || url.pathname === '') {
    // In development, redirect to /en for simplicity
    if (import.meta.env.DEV) {
      return redirect('/en');
    }
    
    // In production, use the user's preferred language
    const acceptLanguage = request.headers?.get('accept-language') || '';
    const preferredLang = parseAcceptLanguage(acceptLanguage);
    return redirect(`/${preferredLang}`);
  }
  
  // For all other requests, let Astro handle the routing
  return next();
};
