// vite.config.js
export default {
  // Allow Cloudflare tunnel domain
  // TODO: Remove or update this when deploying to production
  server: {
    allowedHosts: ['localhost', '*.trycloudflare.com', '*.ngrok-free.app', 'bright-gladly-ocelot.ngrok-free.app'],
    // TODO: Remove or update this when deploying to production
    // This is only needed for Cloudflare tunnel during development
    hmr: {
      clientPort: 3000,
      host: 'localhost'
    },
    // Allow Cloudflare tunnel domain
    host: '0.0.0.0',
    strictPort: true,
    fs: {
      // Allow serving files from the public directory
      allow: ['public']
    },
    // Configure MIME types
    mime: {
      '.woff2': 'font/woff2',
      '.woff': 'font/woff',
      '.ttf': 'font/ttf',
      '.otf': 'font/otf'
    }
  },
  preview: {
    port: 3000,
    strictPort: true,
  },
  // TODO: Remove or update this when deploying to production
  // This is only needed for Cloudflare tunnel during development
  optimizeDeps: {
    exclude: ['@astrojs/']
  },
  // Allow Cloudflare tunnel domain
  // TODO: Remove or update this when deploying to production
  // This is only needed for Cloudflare tunnel during development
  build: {
    commonjsOptions: {
      transformMixedEsModules: true
    }
  }
}
