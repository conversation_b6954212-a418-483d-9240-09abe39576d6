# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands
- `npm run dev` - Start development server
- `npm run build` - Build for production 
- `npm run preview` - Preview production build locally

## Project Information
- Astro.js website with i18n (English, French, Amharic)
- Uses CSS for styling (no CSS frameworks detected)
- Typescript configuration extends Astro base config

## Code Style Guidelines
- Use absolute imports when possible
- Follow existing component and naming patterns
- Maintain multilingual support using i18n utils
- Respect product categorization structure
- Component files use .astro extension with frontmatter
- Page content should be organized under locale directories (en, fr, am)
- Component names should use PascalCase
- CSS classes use kebab-case
- Keep i18n keys organized by section/component
- Use Astro's built-in slot system for content projection