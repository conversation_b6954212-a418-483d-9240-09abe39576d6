document.addEventListener("DOMContentLoaded", () => {
    document.querySelectorAll('.tab-btn').forEach(button => {
        button.addEventListener('click', () => {
          const tab = button.dataset.tab;
      
          document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
          document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

          button.classList.add('active');
          document.getElementById(tab).classList.add('active');
        });
    });    
})