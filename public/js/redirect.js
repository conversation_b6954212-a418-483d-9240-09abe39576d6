// This script handles redirection to the appropriate locale
(function() {
  // Only run on the root path
  if (window.location.pathname === '/' || window.location.pathname === '') {
    // Get the browser language
    const browserLang = navigator.language.split('-')[0];
    
    // List of supported languages
    const supportedLanguages = ['en', 'fr'];
    
    // Default language
    const defaultLang = 'en';
    
    // Determine the preferred language
    const preferredLang = supportedLanguages.includes(browserLang) ? browserLang : defaultLang;
    
    // Redirect to the preferred language
    window.location.href = `/${preferredLang}`;
  }
})();
