document.addEventListener("DOMContentLoaded", () => {
    const popupListeners = new Map();
    
    const popupTriggersHandler = (el) => {
        const popupSelector = el.dataset.popupTrigger;
        const popupView = [...document.querySelectorAll(`[data-popup]${popupSelector}`)].filter(elm => isElementVisible(elm));
        const targetPopupView = popupView[0];
        if (!targetPopupView) return;
    
        targetPopupView.classList.toggle("show");
    
        let cancelButtons;
    
        const toggleShow = () => {
            targetPopupView.classList.remove("show");
            if (cancelButtons.length) {
                cancelButtons.forEach(cancelButton => {
                    cancelButton.removeEventListener("click", toggleShow);
                })
            }
        };
    
        cancelButtons = [...targetPopupView.querySelectorAll("[cancel]")];
        if (cancelButtons.length) {
            cancelButtons.forEach(cancelButton => {
                cancelButton.addEventListener("click", toggleShow);
            })
        }
    
        if (targetPopupView.hasAttribute('auto-close')) {
            const autoCloseHandler = (event) => {
                if (event.target === targetPopupView) {
                    targetPopupView.classList.remove("show");
                    targetPopupView.removeEventListener('click', autoCloseHandler);
                }
            };
            targetPopupView.addEventListener('click', autoCloseHandler);
        }
    }
    
    const isElementVisible = (element) => {
        let currentElement = element;
        while (currentElement) {
            if (getComputedStyle(currentElement).display === 'none') {
                return false;
            }
            currentElement = currentElement.parentElement;
            if (currentElement === document.body) break;
        }
        return true;
    };
    
    const initModals = () => {
        setTimeout(() => {
            const popupTriggers = [...document.querySelectorAll("[data-popup-trigger]")];
            popupTriggers.forEach((el) => {
                const handler = () => popupTriggersHandler(el);
                el.addEventListener("click", handler);
                popupListeners.set(el, handler);
            });
        }, 0);
    }

    initModals()
})