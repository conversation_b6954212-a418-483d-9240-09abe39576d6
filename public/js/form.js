document.addEventListener("DOMContentLoaded", () => {
    const submitFormButton = document.getElementById("contact-us-submit-button")
    const form = document.getElementById("contact-us-form")
    const dangerAlert = document.getElementById("alert-danger")
    const successAlert = document.getElementById("alert-success")

    async function sendEmail(data = {}) {
      const DOMAIN = "greyparrot.io";
      const API_KEY = "**************************************************";
      const url = `https://api.mailgun.net/v3/${DOMAIN}/messages`;

      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            "Authorization": `Basic ${btoa(`api:${API_KEY}`)}`,
            "Content-Type": "application/x-www-form-urlencoded"
          },
          body: data.toString()
        });
        return response;
      } catch (error) {
        console.error('There was a problem with sending email:', error);
        throw error;
      }
    }

    form.addEventListener("submit", () => {
        submitFormButton.classList.add("loading")
        form.classList.add("loading-content")

        const formData = new FormData(form)

        const formObject = {};
        formData.forEach((value, key) => {
            formObject[key] = value;
        });

        const requestBody = new URLSearchParams();
        requestBody.append("from", "<EMAIL>");
        requestBody.append("to", "<EMAIL>");
        requestBody.append("subject", "Customer Enquiry");
        requestBody.append("template", "website contact us");
        requestBody.append("h:X-Mailgun-Variables", JSON.stringify({ 
          "COMPANY_NAME": formObject?.["company-name"],
          "CUSTOMER_NAME": formObject?.["first-name"] + ' ' + formObject?.["last-name"],
          "CUSTOMER_EMAIL": formObject?.["corporate-email"],
          "CUSTOMER_PHONE_NUMBER": formObject?.["phone-number"],
          "CUSTOMER_REQUEST_CUSTOM_DEMO": formObject?.["custom-demo"],
          "CUSTOMER_COMMENTS": formObject?.["comment"]
        }));

        sendEmail(requestBody).then(() => {
            submitFormButton.classList.remove("loading")
            submitFormButton.classList.add("disabled")
            form.classList.remove("loading-content")
            successAlert.classList.remove("d-none")

            // Clear form
            document.getElementById("first-name").value = ""
            document.getElementById("last-name").value = ""
            document.getElementById("company-name").value = ""
            document.getElementById("corporate-email").value = ""
            document.getElementById("phone-number").value = ""
            document.getElementById("custom-demo-no").checked = true
            document.getElementById("comment").value = ""
            
        }).catch((error) => {
            console.error(error)
            submitFormButton.classList.remove("loading")
            dangerAlert.classList.remove("d-none")
            form.classList.remove("loading-content")
        }).finally(() => {
            document.getElementById("contact-us").scrollIntoView({ behavior: "smooth" })
            setTimeout(() => {
                successAlert.classList.add("d-none")
                dangerAlert.classList.add("d-none")
            }, 5000)
        })
        
    })
})