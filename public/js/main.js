document.addEventListener("DOMContentLoaded", () => {
    // Mobile Experience
    let mobileMenuTrigger = document.getElementById("mobile-trigger")
    let mobileMenuContent = document.getElementById("mobile-content")
    mobileMenuTrigger.addEventListener("click", () => {
        mobileMenuContent.classList.toggle("show")
        document.dispatchEvent(new CustomEvent("subContextMenuInteraction", { detail: { isVisible: mobileMenuContent.classList.contains("show"), skip: false } }))
    })

    let mobileMenuItemLinks = [...document.querySelectorAll(".nav-mobile-link")]
    mobileMenuItemLinks.forEach((link) => {
        link.addEventListener("click", () => {
            let nextSibling = link.nextElementSibling
            nextSibling.classList.toggle("show")
            document.dispatchEvent(new CustomEvent("subContextMenuInteraction", { detail: { isVisible: nextSibling.classList.contains("show"), skip: false } }))
        })
    })

    let returnLinks = [...document.querySelectorAll(".nav-mobile-return-link")]
    returnLinks.forEach(returnLink => {
        returnLink.addEventListener("click", () => {
            let parent = returnLink.parentElement
            parent.classList.toggle("show")
            document.dispatchEvent(new CustomEvent("subContextMenuInteraction", { detail: { isVisible: parent.classList.contains("show"), skip: true } }))
        })
    })

    document.querySelector('#mobile-content ul').addEventListener('click', (event) => {
        event.stopPropagation();
    });

    mobileMenuContent.addEventListener('click', () => {
        mobileMenuContent.classList.remove("show")
        document.dispatchEvent(new CustomEvent("subContextMenuInteraction", { detail: { isVisible: false, skip: false } }))
    });

    let scrollPosition = 0
    document.addEventListener("subContextMenuInteraction", ({ detail }) => {
        if (detail.isVisible) {
            scrollPosition = window.scrollY;
            document.body.style.overflowY = "hidden"
            document.body.classList.add("no-scroll")
        } else {
            if (!detail.skip) {
                document.body.style.overflowY = "auto"
                document.body.classList.remove("no-scroll")
                mobileMenuItemLinks.forEach(link => link.nextElementSibling.classList.remove("show"))
            }
            window.scrollTo(0, scrollPosition)
        }
    })

    // Desktop Experience
    let desktopMenuItemLinks = [...document.querySelectorAll(".nav-desktop-link")]

    function handleDocumentClick(event) {
        desktopMenuItemLinks.forEach((link) => {
            let nextSibling = link.nextElementSibling;
            if (nextSibling && nextSibling.classList.contains("show") && !nextSibling.contains(event.target) && event.target !== link) {
                nextSibling.classList.remove("show");
                link.classList.remove("active");
            }
        });
        document.removeEventListener("click", handleDocumentClick);
    }

    function resetDropdowns(exceptLink) {
        desktopMenuItemLinks.forEach(link => {
            if (exceptLink !== link) {
                link.classList.remove("active")
                let nextSibling = link.nextElementSibling;
                if (nextSibling) {
                    nextSibling.classList.remove("show")
                }
            }
        })
    }

    desktopMenuItemLinks.forEach((link) => {
        link.addEventListener("click", (event) => {
            event.stopPropagation()

            const wasActive = link.classList.contains("active");

            resetDropdowns()

            if (!wasActive) {
                link.classList.add("active")
                let nextSibling = link.nextElementSibling
                if (nextSibling) {
                    nextSibling.classList.add("show")
                    document.addEventListener("click", handleDocumentClick);
                }
            }
        })
    })

    

})