
document.addEventListener("DOMContentLoaded", () => {
    const submitFormButton = document.getElementById("request-demo-2-submit-button")
    const form = document.getElementById("request-demo-2-form")
    const dangerAlert = document.getElementById("alert-danger-2")
    const successAlert = document.getElementById("alert-success-2")

    async function sendEmail(data = {}) {
        const DOMAIN = "greyparrot.io";
        const API_KEY = "**************************************************";
        const url = `https://api.mailgun.net/v3/${DOMAIN}/messages`;

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    "Authorization": `Basic ${btoa(`api:${API_KEY}`)}`,
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                body: data.toString()
            });
            return response;
        } catch (error) {
            console.error('There was a problem with sending email:', error);
            throw error;
        }
    }

    form.addEventListener("submit", () => {
        submitFormButton.classList.add("loading")
        form.classList.add("loading-content")

        const formData = new FormData(form)

        const formObject = {};
        formData.forEach((value, key) => {
            formObject[key] = value;
        });

        const requestBody = new URLSearchParams();
        requestBody.append("from", "<EMAIL>");
        requestBody.append("to", "<EMAIL>");
        requestBody.append("subject", "Request for a demo");
        requestBody.append("template", "website request demo");
        requestBody.append("h:X-Mailgun-Variables", JSON.stringify({ 
            "COMPANY_NAME": "",
            "CUSTOMER_NAME": formObject?.["full-name"],
            "CUSTOMER_EMAIL": formObject?.["email"],
            "CUSTOMER_PHONE_NUMBER": "",
            "CUSTOMER_ROLE": "",
            "PRODUCT": formObject?.["product"]
        }));

        sendEmail(requestBody).then(() => {
            submitFormButton.classList.remove("loading")
            submitFormButton.classList.add("disabled")
            form.classList.remove("loading-content")
            successAlert.classList.remove("d-none")

            // Clear form
            document.getElementById("full-name").value = ""
            document.getElementById("email").value = ""
            
        }).catch((error) => {
            console.error(error)
            submitFormButton.classList.remove("loading")
            dangerAlert.classList.remove("d-none")
            form.classList.remove("loading-content")
        }).finally(() => {
            setTimeout(() => {
                successAlert.classList.add("d-none")
                dangerAlert.classList.add("d-none")
            }, 5000)
        })
        
    })
})