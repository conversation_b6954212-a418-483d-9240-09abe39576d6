document.addEventListener("DOMContentLoaded", () => {
  // Product slider
  let sliderContainer = document.getElementById("product-demo-section");
  let previousControls = document.getElementById("product-controls-previous");
  let nextControls = document.getElementById("product-controls-next");

  let averageScrollWidth = sliderContainer.children[0].clientWidth;

  previousControls.addEventListener("click", () => {
    sliderContainer.scrollLeft -= averageScrollWidth;
  });

  nextControls.addEventListener("click", () => {
    sliderContainer.scrollLeft += averageScrollWidth;
  });
});
